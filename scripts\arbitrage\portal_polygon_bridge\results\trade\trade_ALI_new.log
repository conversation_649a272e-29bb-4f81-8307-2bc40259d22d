2025-07-07 11:47:43,884 - trade_ALI - INFO - ================================================================================
2025-07-07 11:47:43,885 - trade_ALI - INFO - 开始执行 ALI 买入交易 - 时间: 2025-07-07 11:47:43
2025-07-07 11:47:43,885 - trade_ALI - INFO - 链: ethereum, 投入金额: 283.33 USDT
2025-07-07 11:47:43,885 - trade_ALI - INFO - 代币地址: ******************************************
2025-07-07 11:47:43,893 - trade_ALI - INFO - ALI: 将在ethereum链上执行买入，代币地址: ******************************************
2025-07-07 11:47:43,893 - trade_ALI - INFO - ALI: 准备使用KyberSwap在ethereum上执行283.33USDT买入ALI交易
2025-07-07 11:47:47,318 - trade_ALI - ERROR - 交易执行失败: 代币余额不足。请求: 283.33 USDT，可用: 18.311218 USDT
2025-07-07 12:19:51,848 - trade_ALI - INFO - ================================================================================
2025-07-07 12:19:51,848 - trade_ALI - INFO - 开始执行 ALI 买入交易 - 时间: 2025-07-07 12:19:51
2025-07-07 12:19:51,848 - trade_ALI - INFO - 链: ethereum, 投入金额: 300.0 USDT
2025-07-07 12:19:51,848 - trade_ALI - INFO - 代币地址: ******************************************
2025-07-07 12:19:51,858 - trade_ALI - INFO - ALI: 将在ethereum链上执行买入，代币地址: ******************************************
2025-07-07 12:19:51,858 - trade_ALI - INFO - ALI: 准备使用KyberSwap在ethereum上执行300.0USDT买入ALI交易
2025-07-07 12:19:53,980 - trade_ALI - ERROR - 交易执行失败: 代币余额不足。请求: 300.0 USDT，可用: 18.311218 USDT
2025-07-12 09:23:23,823 - trade_ALI - INFO - ================================================================================
2025-07-12 09:23:23,823 - trade_ALI - INFO - 开始执行 ALI 买入交易 - 时间: 2025-07-12 09:23:23
2025-07-12 09:23:23,824 - trade_ALI - INFO - 链: ethereum, 投入金额: 300.0 USDT
2025-07-12 09:23:23,824 - trade_ALI - INFO - 代币地址: ******************************************
2025-07-12 09:23:23,830 - trade_ALI - INFO - ALI: 将在ethereum链上执行买入，代币地址: ******************************************
2025-07-12 09:23:23,831 - trade_ALI - INFO - ALI: 准备使用KyberSwap在ethereum上执行300.0USDT买入ALI交易
2025-07-12 09:23:43,973 - trade_ALI - INFO - ALI: 使用tx_token_change_tracker获取交易 0xa7af55981074bacd35b32b08f30324edc21310e00bad6a9773ea091938e92507 的代币数量...
2025-07-12 09:23:45,868 - trade_ALI - INFO - ALI: 从tx_token_change_tracker成功获取到代币数量: 54780.819105296058100168
2025-07-12 09:23:45,868 - trade_ALI - INFO - 交易执行成功:
2025-07-12 09:23:45,869 - trade_ALI - INFO -   交易哈希: 0xa7af55981074bacd35b32b08f30324edc21310e00bad6a9773ea091938e92507
2025-07-12 09:23:45,869 - trade_ALI - INFO -   实际输出数量: 54780.81910529606 ALI
2025-07-12 09:23:45,869 - trade_ALI - INFO - 开始执行桥接操作，方向: ethereum_to_polygon
2025-07-12 09:23:45,869 - trade_ALI - INFO - ================================================================================
2025-07-12 09:23:45,869 - trade_ALI - INFO - 开始执行 ALI 桥接操作 - 时间: 2025-07-12 09:23:45
2025-07-12 09:23:45,869 - trade_ALI - INFO - 桥接方向: ethereum_to_polygon
2025-07-12 09:23:45,869 - trade_ALI - INFO - 代币数量: 54780.819105296058100168 ALI
2025-07-12 09:24:08,497 - trade_ALI - INFO - 从以太坊桥接到Polygon: 54780.819105296058100168 ALI
2025-07-12 09:43:34,405 - trade_ALI - INFO - 桥接操作成功完成
2025-07-12 09:43:34,406 - trade_ALI - INFO - 桥接交易哈希: 0xc6ae146bcc8a96b5c191a0f342f6cfd83a18530d065e7725ca3a6f7017139c17
2025-07-12 09:43:34,406 - trade_ALI - INFO - Polygon交易哈希: None
2025-07-12 09:43:34,407 - trade_ALI - INFO - 桥接记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\bridge\ALI_20250712_094334_bridge.json
2025-07-12 09:43:34,408 - trade_ALI - INFO - 桥接操作成功完成
2025-07-12 09:43:34,408 - trade_ALI - INFO - 开始执行卖出操作...
2025-07-12 09:43:34,409 - trade_ALI - INFO - ================================================================================
2025-07-12 09:43:34,409 - trade_ALI - INFO - 开始执行 ALI 卖出交易 - 时间: 2025-07-12 09:43:34
2025-07-12 09:43:34,418 - trade_ALI - INFO - 目标链: polygon
2025-07-12 09:43:34,418 - trade_ALI - INFO - 目标链代币地址: ******************************************
2025-07-12 09:43:34,418 - trade_ALI - INFO - 在polygon链上执行卖出操作
2025-07-12 09:43:34,418 - trade_ALI - INFO - 代币地址: ******************************************
2025-07-12 09:43:34,418 - trade_ALI - INFO - 卖出数量: 54780.819105296058100168 ALI
2025-07-12 09:43:34,418 - trade_ALI - INFO - 预期USDT输出: 303.081318 USDT
2025-07-12 09:43:39,294 - trade_ALI - INFO - 执行卖出交易，预期USDT输出: 303.081318
2025-07-12 09:44:39,457 - trade_ALI - INFO - 使用tx_token_change_tracker获取交易 0xd68cdc0e82e65419de7623b4d17e327589cea2e4d22dcf5c0ea35417bae5f526 的USDT数量...
2025-07-12 09:44:53,048 - trade_ALI - INFO - 从tx_token_change_tracker成功获取到USDT数量
2025-07-12 09:44:53,048 - trade_ALI - INFO - 成功获取到USDT数量: 303.61556
2025-07-12 09:44:53,048 - trade_ALI - INFO - 卖出交易执行成功:
2025-07-12 09:44:53,048 - trade_ALI - INFO -   交易哈希: 0xd68cdc0e82e65419de7623b4d17e327589cea2e4d22dcf5c0ea35417bae5f526
2025-07-12 09:44:53,048 - trade_ALI - INFO -   实际收到USDT数量: 303.61556
2025-07-12 09:44:53,049 - trade_ALI - INFO - 卖出记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\sell\ALI_20250712_094453_sell.json
2025-07-12 09:44:53,049 - trade_ALI - INFO - 卖出操作成功完成
2025-07-12 09:44:53,049 - trade_ALI - INFO - 交易执行完成，耗时: 1289.23秒
2025-07-12 09:44:53,049 - trade_ALI - INFO - ================================================================================
2025-07-12 10:12:28,087 - trade_ALI - INFO - ================================================================================
2025-07-12 10:12:28,088 - trade_ALI - INFO - 开始执行 ALI 买入交易 - 时间: 2025-07-12 10:12:28
2025-07-12 10:12:28,089 - trade_ALI - INFO - 链: ethereum, 投入金额: 150.0 USDT
2025-07-12 10:12:28,089 - trade_ALI - INFO - 代币地址: ******************************************
2025-07-12 10:12:28,103 - trade_ALI - INFO - ALI: 将在ethereum链上执行买入，代币地址: ******************************************
2025-07-12 10:12:28,103 - trade_ALI - INFO - ALI: 准备使用KyberSwap在ethereum上执行150.0USDT买入ALI交易
2025-07-12 10:12:43,223 - trade_ALI - INFO - ALI: 使用tx_token_change_tracker获取交易 0xcbb30fd4f0f6245dba6771031894d293d7739cd87055478985236149812c3a70 的代币数量...
2025-07-12 10:12:56,362 - trade_ALI - INFO - ALI: 从tx_token_change_tracker成功获取到代币数量: 27362.682740650477213371
2025-07-12 10:12:56,363 - trade_ALI - INFO - 交易执行成功:
2025-07-12 10:12:56,364 - trade_ALI - INFO -   交易哈希: 0xcbb30fd4f0f6245dba6771031894d293d7739cd87055478985236149812c3a70
2025-07-12 10:12:56,364 - trade_ALI - INFO -   实际输出数量: 27362.682740650478 ALI
2025-07-12 10:12:56,364 - trade_ALI - INFO - 开始执行桥接操作，方向: ethereum_to_polygon
2025-07-12 10:12:56,364 - trade_ALI - INFO - ================================================================================
2025-07-12 10:12:56,365 - trade_ALI - INFO - 开始执行 ALI 桥接操作 - 时间: 2025-07-12 10:12:56
2025-07-12 10:12:56,367 - trade_ALI - INFO - 桥接方向: ethereum_to_polygon
2025-07-12 10:12:56,367 - trade_ALI - INFO - 代币数量: 27362.682740650477213371 ALI
2025-07-12 10:13:19,027 - trade_ALI - INFO - 从以太坊桥接到Polygon: 27362.682740650477213371 ALI
2025-07-12 10:34:34,262 - trade_ALI - INFO - 桥接操作成功完成
2025-07-12 10:34:34,262 - trade_ALI - INFO - 桥接交易哈希: 0xfcf1161b28181e37f2b4194ee85a2513891cf4d6f7e0cecfd53c9475877734c1
2025-07-12 10:34:34,262 - trade_ALI - INFO - Polygon交易哈希: None
2025-07-12 10:34:34,263 - trade_ALI - INFO - 桥接记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\bridge\ALI_20250712_103434_bridge.json
2025-07-12 10:34:34,263 - trade_ALI - INFO - 桥接操作成功完成
2025-07-12 10:34:34,263 - trade_ALI - INFO - 开始执行卖出操作...
2025-07-12 10:34:34,263 - trade_ALI - INFO - ================================================================================
2025-07-12 10:34:34,263 - trade_ALI - INFO - 开始执行 ALI 卖出交易 - 时间: 2025-07-12 10:34:34
2025-07-12 10:34:34,269 - trade_ALI - INFO - 目标链: polygon
2025-07-12 10:34:34,269 - trade_ALI - INFO - 目标链代币地址: ******************************************
2025-07-12 10:34:34,269 - trade_ALI - INFO - 在polygon链上执行卖出操作
2025-07-12 10:34:34,269 - trade_ALI - INFO - 代币地址: ******************************************
2025-07-12 10:34:34,269 - trade_ALI - INFO - 卖出数量: 27362.682740650477213371 ALI
2025-07-12 10:34:34,269 - trade_ALI - INFO - 预期USDT输出: 153.79455 USDT
2025-07-12 10:34:36,584 - trade_ALI - INFO - 执行卖出交易，预期USDT输出: 153.79455
2025-07-12 10:35:27,730 - trade_ALI - ERROR - swap_result详细信息: {'success': False, 'status': '输出不足', 'message': '', 'error': None, 'chain': 'polygon', 'token_in': '******************************************', 'token_out': '0xc2132d05d31c914a87c6611c10748aeb04b58e8f', 'amount': 27362.682740650478, 'token_in_address': '******************************************', 'token_out_address': '0xc2132d05d31c914a87c6611c10748aeb04b58e8f', 'amount_in_wei': '27362682740650478141440', 'is_native_in': False, 'is_native_out': False}
2025-07-12 10:35:27,731 - trade_ALI - ERROR - 解析后的错误信息: error_msg='未知错误', status='输出不足'
2025-07-12 10:35:27,731 - trade_ALI - WARNING - 卖出预验证失败: 未知错误
2025-07-12 10:35:27,731 - trade_ALI - INFO - 预验证失败，启动最优链选择策略...
2025-07-12 10:35:27,731 - trade_ALI - INFO - ================================================================================
2025-07-12 10:35:27,731 - trade_ALI - INFO - 开始执行 ALI 最优链卖出交易 - 时间: 2025-07-12 10:35:27
2025-07-12 10:35:27,732 - trade_ALI - INFO - ================================================================================
2025-07-12 10:35:27,732 - trade_ALI - INFO - 开始寻找 ALI 的最优卖出链 - 时间: 2025-07-12 10:35:27
2025-07-12 10:35:27,732 - trade_ALI - INFO - 代币数量: 27362.682740650477213371 ALI
2025-07-12 10:35:33,657 - trade_ALI - INFO - Polygon地址: ******************************************
2025-07-12 10:35:33,658 - trade_ALI - INFO - Ethereum地址: ******************************************
2025-07-12 10:35:33,658 - trade_ALI - INFO - 检查Polygon链卖出价格...
2025-07-12 10:35:38,677 - trade_ALI - INFO - 检查Ethereum链卖出价格...
2025-07-12 10:36:15,208 - trade_ALI - INFO - Polygon链预期输出: 151.763271 USDT, Gas成本: 0.005819667445707875 USDT
2025-07-12 10:36:15,208 - trade_ALI - INFO - Ethereum链预期输出: 149.846593 USDT, Gas成本: 1.063747112391676 USDT
2025-07-12 10:36:15,208 - trade_ALI - INFO - 两链USDT输出差异: 1.9166779999999903 USDT
2025-07-12 10:36:15,208 - trade_ALI - INFO - 当前链(polygon)输出: 151.763271 USDT, Gas成本: 0.005819667445707875 USDT
2025-07-12 10:36:15,208 - trade_ALI - INFO - 另一条链(ethereum)输出: 149.846593 USDT, Gas成本: 1.063747112391676 USDT
2025-07-12 10:36:15,209 - trade_ALI - INFO - 输出差异(1.9166779999999903 USDT)小于2 USDT，选择在当前链(polygon)执行卖出
2025-07-12 10:36:15,209 - trade_ALI - INFO - 最优卖出链: polygon
2025-07-12 10:36:15,209 - trade_ALI - INFO - 选择原因: 输出差异(1.92 USDT)小于2 USDT
2025-07-12 10:36:15,209 - trade_ALI - INFO - 是否需要跨链: 否
2025-07-12 10:36:15,218 - trade_ALI - INFO - 最终执行链: polygon
2025-07-12 10:36:15,218 - trade_ALI - INFO - 代币地址: ******************************************
2025-07-12 10:36:15,219 - trade_ALI - INFO - 卖出数量: 27362.682740650477213371 ALI
2025-07-12 10:36:15,219 - trade_ALI - INFO - 在最优链执行卖出交易（无预期输出验证）
2025-07-12 10:36:54,655 - trade_ALI - INFO - 使用tx_token_change_tracker获取交易 0xf95620de3756117bbf25ef02eda57474ffe4f70dae48572843ddc4eb11258d89 的USDT数量...
2025-07-12 10:37:09,440 - trade_ALI - INFO - 从tx_token_change_tracker成功获取到USDT数量
2025-07-12 10:37:09,440 - trade_ALI - INFO - 成功获取到USDT数量: 151.738394
2025-07-12 10:37:09,441 - trade_ALI - INFO - 最优链卖出交易执行成功:
2025-07-12 10:37:09,441 - trade_ALI - INFO -   执行链: polygon
2025-07-12 10:37:09,441 - trade_ALI - INFO -   交易哈希: 0xf95620de3756117bbf25ef02eda57474ffe4f70dae48572843ddc4eb11258d89
2025-07-12 10:37:09,441 - trade_ALI - INFO -   实际收到USDT数量: 151.738394
2025-07-12 10:37:09,442 - trade_ALI - INFO - 卖出记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\sell\ALI_20250712_103709_sell.json
2025-07-12 10:37:09,442 - trade_ALI - INFO - 最优链选择策略执行成功
2025-07-12 10:37:09,442 - trade_ALI - INFO - 卖出操作成功完成
2025-07-12 10:37:09,442 - trade_ALI - INFO - 交易执行完成，耗时: 1481.35秒
2025-07-12 10:37:09,442 - trade_ALI - INFO - ================================================================================
2025-07-12 15:09:10,478 - trade_ALI - INFO - ================================================================================
2025-07-12 15:09:10,479 - trade_ALI - INFO - 开始执行 ALI 买入交易 - 时间: 2025-07-12 15:09:10
2025-07-12 15:09:10,479 - trade_ALI - INFO - 链: ethereum, 投入金额: 300.0 USDT
2025-07-12 15:09:10,479 - trade_ALI - INFO - 代币地址: ******************************************
2025-07-12 15:09:10,496 - trade_ALI - INFO - ALI: 将在ethereum链上执行买入，代币地址: ******************************************
2025-07-12 15:09:10,496 - trade_ALI - INFO - ALI: 准备使用KyberSwap在ethereum上执行300.0USDT买入ALI交易
2025-07-12 15:09:26,713 - trade_ALI - INFO - ALI: 使用tx_token_change_tracker获取交易 0xb4fb16ad26a7d676d79f50a02351cfb47186bb14e9ece6e561908d3048dc0f61 的代币数量...
2025-07-12 15:09:32,421 - trade_ALI - INFO - ALI: 从tx_token_change_tracker成功获取到代币数量: 55063.030221566048973613
2025-07-12 15:09:32,421 - trade_ALI - INFO - 交易执行成功:
2025-07-12 15:09:32,422 - trade_ALI - INFO -   交易哈希: 0xb4fb16ad26a7d676d79f50a02351cfb47186bb14e9ece6e561908d3048dc0f61
2025-07-12 15:09:32,422 - trade_ALI - INFO -   实际输出数量: 55063.03022156605 ALI
2025-07-12 15:09:32,422 - trade_ALI - INFO - 开始执行桥接操作，方向: ethereum_to_polygon
2025-07-12 15:09:32,422 - trade_ALI - INFO - ================================================================================
2025-07-12 15:09:32,422 - trade_ALI - INFO - 开始执行 ALI 桥接操作 - 时间: 2025-07-12 15:09:32
2025-07-12 15:09:32,422 - trade_ALI - INFO - 桥接方向: ethereum_to_polygon
2025-07-12 15:09:32,422 - trade_ALI - INFO - 代币数量: 55063.030221566048973613 ALI
2025-07-12 15:10:32,508 - trade_ALI - ERROR - 执行桥接操作时出错: Bridge初始化失败，已达到最大重试次数: 无法连接到以太坊节点
2025-07-12 15:10:32,509 - trade_ALI - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\bridge\pol_bridge\auto_bridge.py", line 56, in __init__
    self.bridge = PolygonEthereumBridge(
                  ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\bridge\pol_bridge\bridge_tokens.py", line 111, in __init__
    raise ConnectionError("无法连接到以太坊节点")
ConnectionError: 无法连接到以太坊节点

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\bridge_arb_executor.py", line 673, in bridge_tokens
    bridge = AutoBridge()
             ^^^^^^^^^^^^
  File "C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\bridge\pol_bridge\auto_bridge.py", line 94, in __init__
    raise ValueError(f"Bridge初始化失败，已达到最大重试次数: {str(last_error)}")
ValueError: Bridge初始化失败，已达到最大重试次数: 无法连接到以太坊节点

2025-07-12 15:10:32,509 - trade_ALI - ERROR - 桥接操作失败: 执行桥接操作时出错: Bridge初始化失败，已达到最大重试次数: 无法连接到以太坊节点
2025-07-12 15:10:32,509 - trade_ALI - INFO - 交易执行完成，耗时: 82.03秒
2025-07-12 15:10:32,509 - trade_ALI - INFO - ================================================================================
2025-07-12 15:52:32,504 - trade_ALI - INFO - ================================================================================
2025-07-12 15:52:32,505 - trade_ALI - INFO - 开始执行 ALI 买入交易 - 时间: 2025-07-12 15:52:32
2025-07-12 15:52:32,506 - trade_ALI - INFO - 链: ethereum, 投入金额: 300.0 USDT
2025-07-12 15:52:32,507 - trade_ALI - INFO - 代币地址: ******************************************
2025-07-12 15:52:32,519 - trade_ALI - INFO - ALI: 将在ethereum链上执行买入，代币地址: ******************************************
2025-07-12 15:52:32,519 - trade_ALI - INFO - ALI: 准备使用KyberSwap在ethereum上执行300.0USDT买入ALI交易
2025-07-12 15:53:51,120 - trade_ALI - INFO - ALI: 使用tx_token_change_tracker获取交易 0xdcd6c9bb676e77c0e169375201a131dd3ed917cdae25ecbb9dc1b10fd324d3ae 的代币数量...
2025-07-12 15:53:58,568 - trade_ALI - INFO - ALI: 从tx_token_change_tracker成功获取到代币数量: 54754.339376112053210061
2025-07-12 15:53:58,568 - trade_ALI - INFO - 交易执行成功:
2025-07-12 15:53:58,568 - trade_ALI - INFO -   交易哈希: 0xdcd6c9bb676e77c0e169375201a131dd3ed917cdae25ecbb9dc1b10fd324d3ae
2025-07-12 15:53:58,569 - trade_ALI - INFO -   实际输出数量: 54754.33937611205 ALI
2025-07-12 15:53:58,569 - trade_ALI - INFO - 开始执行桥接操作，方向: ethereum_to_polygon
2025-07-12 15:53:58,569 - trade_ALI - INFO - ================================================================================
2025-07-12 15:53:58,569 - trade_ALI - INFO - 开始执行 ALI 桥接操作 - 时间: 2025-07-12 15:53:58
2025-07-12 15:53:58,569 - trade_ALI - INFO - 桥接方向: ethereum_to_polygon
2025-07-12 15:53:58,569 - trade_ALI - INFO - 代币数量: 54754.339376112053210061 ALI
2025-07-12 15:54:11,113 - trade_ALI - INFO - 从以太坊桥接到Polygon: 54754.339376112053210061 ALI
2025-07-12 16:13:46,496 - trade_ALI - INFO - 桥接操作成功完成
2025-07-12 16:13:46,496 - trade_ALI - INFO - 桥接交易哈希: 0xf713002063178d6ddcf21120dfc8430a4e3c6dc477a711c43f07320af1e6827f
2025-07-12 16:13:46,496 - trade_ALI - INFO - Polygon交易哈希: None
2025-07-12 16:13:46,498 - trade_ALI - INFO - 桥接记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\bridge\ALI_20250712_161346_bridge.json
2025-07-12 16:13:46,499 - trade_ALI - INFO - 桥接操作成功完成
2025-07-12 16:13:46,499 - trade_ALI - INFO - 开始执行卖出操作...
2025-07-12 16:13:46,499 - trade_ALI - INFO - ================================================================================
2025-07-12 16:13:46,499 - trade_ALI - INFO - 开始执行 ALI 卖出交易 - 时间: 2025-07-12 16:13:46
2025-07-12 16:13:46,506 - trade_ALI - INFO - 目标链: polygon
2025-07-12 16:13:46,506 - trade_ALI - INFO - 目标链代币地址: ******************************************
2025-07-12 16:13:46,506 - trade_ALI - INFO - 在polygon链上执行卖出操作
2025-07-12 16:13:46,506 - trade_ALI - INFO - 代币地址: ******************************************
2025-07-12 16:13:46,506 - trade_ALI - INFO - 卖出数量: 54754.339376112053210061 ALI
2025-07-12 16:13:46,507 - trade_ALI - INFO - 预期USDT输出: 303.493906 USDT
2025-07-12 16:13:50,389 - trade_ALI - INFO - 执行卖出交易，预期USDT输出: 303.493906
2025-07-12 16:14:20,664 - trade_ALI - ERROR - swap_result详细信息: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 54754.33937611205 ******************************************，可用: 27362.682740650478 ******************************************', 'chain': 'polygon', 'token_in': '******************************************', 'token_out': '0xc2132d05d31c914a87c6611c10748aeb04b58e8f', 'amount': 54754.33937611205, 'token_in_address': '******************************************', 'token_out_address': '0xc2132d05d31c914a87c6611c10748aeb04b58e8f', 'amount_in_wei': '54754339376112053780480', 'is_native_in': False, 'is_native_out': False}
2025-07-12 16:14:20,665 - trade_ALI - ERROR - 解析后的错误信息: error_msg='代币余额不足。请求: 54754.33937611205 ******************************************，可用: 27362.682740650478 ******************************************', status='失败'
2025-07-12 16:14:20,665 - trade_ALI - ERROR - 卖出交易执行失败: 代币余额不足。请求: 54754.33937611205 ******************************************，可用: 27362.682740650478 ******************************************
2025-07-12 16:14:20,665 - trade_ALI - ERROR - 卖出操作失败: 代币余额不足。请求: 54754.33937611205 ******************************************，可用: 27362.682740650478 ******************************************
2025-07-12 16:14:20,665 - trade_ALI - ERROR - 最终卖出操作失败: 代币余额不足。请求: 54754.33937611205 ******************************************，可用: 27362.682740650478 ******************************************
2025-07-12 16:14:20,665 - trade_ALI - INFO - 交易执行完成，耗时: 1308.16秒
2025-07-12 16:14:20,665 - trade_ALI - INFO - ================================================================================
