2025-07-06 18:34:02,861 - trade_INSUR - INFO - ================================================================================
2025-07-06 18:34:02,861 - trade_INSUR - INFO - 开始执行 INSUR 买入交易 - 时间: 2025-07-06 18:34:02
2025-07-06 18:34:02,861 - trade_INSUR - INFO - 链: polygon, 投入金额: 10.28 USDT
2025-07-06 18:34:02,861 - trade_INSUR - INFO - 代币地址: ******************************************
2025-07-06 18:34:02,861 - trade_INSUR - INFO - INSUR: 将在polygon链上执行买入，代币地址: ******************************************
2025-07-06 18:34:02,861 - trade_INSUR - INFO - INSUR: 准备使用KyberSwap在polygon上执行10.28USDT买入INSUR交易
2025-07-06 18:34:14,552 - trade_INSUR - INFO - INSUR: 使用tx_token_change_tracker获取交易 0x8a3bf3065a8dbf3b62cee178451e4456ee271abbd1c11e402fe4f613be0de549 的代币数量...
2025-07-06 18:34:18,700 - trade_INSUR - INFO - INSUR: 从tx_token_change_tracker成功获取到代币数量: 24954.461692647496312821
2025-07-06 18:34:18,700 - trade_INSUR - INFO - 交易执行成功:
2025-07-06 18:34:18,700 - trade_INSUR - INFO -   交易哈希: 0x8a3bf3065a8dbf3b62cee178451e4456ee271abbd1c11e402fe4f613be0de549
2025-07-06 18:34:18,700 - trade_INSUR - INFO -   实际输出数量: 24954.461692647496 INSUR
2025-07-06 18:34:18,700 - trade_INSUR - INFO - 开始执行桥接操作，方向: polygon_to_ethereum
2025-07-06 18:34:18,700 - trade_INSUR - INFO - ================================================================================
2025-07-06 18:34:18,700 - trade_INSUR - INFO - 开始执行 INSUR 桥接操作 - 时间: 2025-07-06 18:34:18
2025-07-06 18:34:18,700 - trade_INSUR - INFO - 桥接方向: polygon_to_ethereum
2025-07-06 18:34:18,700 - trade_INSUR - INFO - 代币数量: 24954.461692647496312821 INSUR
2025-07-06 18:34:23,154 - trade_INSUR - INFO - 从Polygon桥接到以太坊: 24954.461692647496312821 INSUR
2025-07-06 19:11:11,498 - trade_INSUR - INFO - 桥接操作成功完成
2025-07-06 19:11:11,499 - trade_INSUR - INFO - Burn交易哈希: 0x257ae605323a20e22782367f85152230b650f75931053f00ec2f47a84011d96d
2025-07-06 19:11:11,500 - trade_INSUR - INFO - Claim交易哈希: 0x90281e3014070badf6ed225128c7d7e3136afd9db4e8c754d446758fc2169fb4
2025-07-06 19:11:11,503 - trade_INSUR - INFO - 桥接记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\bridge\INSUR_20250706_191111_bridge.json
2025-07-06 19:11:11,504 - trade_INSUR - INFO - 桥接操作成功完成
2025-07-06 19:11:11,504 - trade_INSUR - INFO - 开始执行卖出操作...
2025-07-06 19:11:11,506 - trade_INSUR - INFO - ================================================================================
2025-07-06 19:11:11,506 - trade_INSUR - INFO - 开始执行 INSUR 卖出交易 - 时间: 2025-07-06 19:11:11
2025-07-06 19:11:11,514 - trade_INSUR - INFO - 目标链: ethereum
2025-07-06 19:11:11,515 - trade_INSUR - INFO - 目标链代币地址: ******************************************
2025-07-06 19:11:11,515 - trade_INSUR - INFO - 在ethereum链上执行卖出操作
2025-07-06 19:11:11,516 - trade_INSUR - INFO - 代币地址: ******************************************
2025-07-06 19:11:11,516 - trade_INSUR - INFO - 卖出数量: 24954.461692647496312821 INSUR
2025-07-06 19:11:11,516 - trade_INSUR - INFO - 预期USDT输出: 12.736902 USDT
2025-07-06 19:11:15,275 - trade_INSUR - INFO - 执行卖出交易，预期USDT输出: 12.736902
2025-07-06 19:11:38,014 - trade_INSUR - INFO - 使用tx_token_change_tracker获取交易 0xe9b202ee2d258b1a474856ca997a45c86950a700ff3572474057c7ac755c0108 的USDT数量...
2025-07-06 19:11:39,215 - trade_INSUR - INFO - 从tx_token_change_tracker成功获取到USDT数量
2025-07-06 19:11:39,216 - trade_INSUR - INFO - 成功获取到USDT数量: 12.735894
2025-07-06 19:11:39,216 - trade_INSUR - INFO - 卖出交易执行成功:
2025-07-06 19:11:39,216 - trade_INSUR - INFO -   交易哈希: 0xe9b202ee2d258b1a474856ca997a45c86950a700ff3572474057c7ac755c0108
2025-07-06 19:11:39,216 - trade_INSUR - INFO -   实际收到USDT数量: 12.735894
2025-07-06 19:11:39,217 - trade_INSUR - INFO - 卖出记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\sell\INSUR_20250706_191139_sell.json
2025-07-06 19:11:39,217 - trade_INSUR - INFO - 卖出操作成功完成
2025-07-06 19:11:39,218 - trade_INSUR - INFO - 交易执行完成，耗时: 2256.36秒
2025-07-06 19:11:39,218 - trade_INSUR - INFO - ================================================================================
2025-07-12 11:23:34,359 - trade_INSUR - INFO - ================================================================================
2025-07-12 11:23:34,359 - trade_INSUR - INFO - 开始执行 INSUR 买入交易 - 时间: 2025-07-12 11:23:34
2025-07-12 11:23:34,359 - trade_INSUR - INFO - 链: polygon, 投入金额: 10.28 USDT
2025-07-12 11:23:34,359 - trade_INSUR - INFO - 代币地址: ******************************************
2025-07-12 11:23:34,366 - trade_INSUR - INFO - INSUR: 将在polygon链上执行买入，代币地址: ******************************************
2025-07-12 11:23:34,366 - trade_INSUR - INFO - INSUR: 准备使用KyberSwap在polygon上执行10.28USDT买入INSUR交易
2025-07-12 11:25:21,123 - trade_INSUR - INFO - INSUR: 使用tx_token_change_tracker获取交易 0x054d63978a337e97eb49927e40080c3ff2b81b0555ea6e75cb2ee9ffe01302a4 的代币数量...
2025-07-12 11:25:25,216 - trade_INSUR - INFO - INSUR: 从tx_token_change_tracker成功获取到代币数量: 31814.933636088947616968
2025-07-12 11:25:25,217 - trade_INSUR - INFO - 交易执行成功:
2025-07-12 11:25:25,217 - trade_INSUR - INFO -   交易哈希: 0x054d63978a337e97eb49927e40080c3ff2b81b0555ea6e75cb2ee9ffe01302a4
2025-07-12 11:25:25,217 - trade_INSUR - INFO -   实际输出数量: 31814.933636088947 INSUR
2025-07-12 11:25:25,217 - trade_INSUR - INFO - 开始执行桥接操作，方向: polygon_to_ethereum
2025-07-12 11:25:25,217 - trade_INSUR - INFO - ================================================================================
2025-07-12 11:25:25,217 - trade_INSUR - INFO - 开始执行 INSUR 桥接操作 - 时间: 2025-07-12 11:25:25
2025-07-12 11:25:25,217 - trade_INSUR - INFO - 桥接方向: polygon_to_ethereum
2025-07-12 11:25:25,217 - trade_INSUR - INFO - 代币数量: 31814.933636088947616968 INSUR
2025-07-12 11:26:11,740 - trade_INSUR - INFO - 从Polygon桥接到以太坊: 31814.933636088947616968 INSUR
2025-07-12 12:24:13,321 - trade_INSUR - INFO - 桥接操作成功完成
2025-07-12 12:24:13,322 - trade_INSUR - INFO - Burn交易哈希: 0x274ed7aa53168e7a2210d1e842497fe5ef09ff3c2d5239b1b065bdd3afd2b26d
2025-07-12 12:24:13,322 - trade_INSUR - INFO - Claim交易哈希: 0x5a469046b84681c43c553d27c1329d2ffe0e3c87b13832e936d2f9b7c9d05eb3
2025-07-12 12:24:13,323 - trade_INSUR - INFO - 桥接记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\bridge\INSUR_20250712_122413_bridge.json
2025-07-12 12:24:13,324 - trade_INSUR - INFO - 桥接操作成功完成
2025-07-12 12:24:13,324 - trade_INSUR - INFO - 开始执行卖出操作...
2025-07-12 12:24:13,324 - trade_INSUR - INFO - ================================================================================
2025-07-12 12:24:13,324 - trade_INSUR - INFO - 开始执行 INSUR 卖出交易 - 时间: 2025-07-12 12:24:13
2025-07-12 12:24:13,331 - trade_INSUR - INFO - 目标链: ethereum
2025-07-12 12:24:13,331 - trade_INSUR - INFO - 目标链代币地址: ******************************************
2025-07-12 12:24:13,331 - trade_INSUR - INFO - 在ethereum链上执行卖出操作
2025-07-12 12:24:13,331 - trade_INSUR - INFO - 代币地址: ******************************************
2025-07-12 12:24:13,331 - trade_INSUR - INFO - 卖出数量: 31814.933636088947616968 INSUR
2025-07-12 12:24:13,331 - trade_INSUR - INFO - 预期USDT输出: 15.496701 USDT
2025-07-12 12:24:27,287 - trade_INSUR - INFO - 执行卖出交易，预期USDT输出: 15.496701
2025-07-12 12:24:34,280 - trade_INSUR - ERROR - swap_result详细信息: {'success': False, 'status': '输出不足', 'message': '', 'error': None, 'chain': 'ethereum', 'token_in': '******************************************', 'token_out': '******************************************', 'amount': 31814.933636088947, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '31814933636088946753536', 'is_native_in': False, 'is_native_out': False}
2025-07-12 12:24:34,280 - trade_INSUR - ERROR - 解析后的错误信息: error_msg='未知错误', status='输出不足'
2025-07-12 12:24:34,281 - trade_INSUR - WARNING - 卖出预验证失败: 未知错误
2025-07-12 12:24:34,281 - trade_INSUR - INFO - 预验证失败，启动最优链选择策略...
2025-07-12 12:24:34,281 - trade_INSUR - INFO - ================================================================================
2025-07-12 12:24:34,281 - trade_INSUR - INFO - 开始执行 INSUR 最优链卖出交易 - 时间: 2025-07-12 12:24:34
2025-07-12 12:24:34,282 - trade_INSUR - INFO - ================================================================================
2025-07-12 12:24:34,282 - trade_INSUR - INFO - 开始寻找 INSUR 的最优卖出链 - 时间: 2025-07-12 12:24:34
2025-07-12 12:24:34,282 - trade_INSUR - INFO - 代币数量: 31814.933636088947616968 INSUR
2025-07-12 12:24:37,339 - trade_INSUR - INFO - Polygon地址: ******************************************
2025-07-12 12:24:37,339 - trade_INSUR - INFO - Ethereum地址: ******************************************
2025-07-12 12:24:37,340 - trade_INSUR - INFO - 检查Polygon链卖出价格...
2025-07-12 12:24:53,917 - trade_INSUR - INFO - 检查Ethereum链卖出价格...
2025-07-12 12:25:00,879 - trade_INSUR - INFO - Polygon链预期输出: 10.14735 USDT, Gas成本: 0.0023255405178803533 USDT
2025-07-12 12:25:00,879 - trade_INSUR - INFO - Ethereum链预期输出: 9.637653 USDT, Gas成本: 0.6835145951733161 USDT
2025-07-12 12:25:00,879 - trade_INSUR - INFO - 两链USDT输出差异: 0.5096969999999992 USDT
2025-07-12 12:25:00,879 - trade_INSUR - INFO - 当前链(ethereum)输出: 9.637653 USDT, Gas成本: 0.6835145951733161 USDT
2025-07-12 12:25:00,879 - trade_INSUR - INFO - 另一条链(polygon)输出: 10.14735 USDT, Gas成本: 0.0023255405178803533 USDT
2025-07-12 12:25:00,879 - trade_INSUR - INFO - 输出差异(0.5096969999999992 USDT)小于2 USDT，选择在当前链(ethereum)执行卖出
2025-07-12 12:25:00,880 - trade_INSUR - INFO - 最优卖出链: ethereum
2025-07-12 12:25:00,880 - trade_INSUR - INFO - 选择原因: 输出差异(0.51 USDT)小于2 USDT
2025-07-12 12:25:00,880 - trade_INSUR - INFO - 是否需要跨链: 否
2025-07-12 12:25:00,885 - trade_INSUR - INFO - 最终执行链: ethereum
2025-07-12 12:25:00,885 - trade_INSUR - INFO - 代币地址: ******************************************
2025-07-12 12:25:00,885 - trade_INSUR - INFO - 卖出数量: 31814.933636088947616968 INSUR
2025-07-12 12:25:00,885 - trade_INSUR - INFO - 在最优链执行卖出交易（无预期输出验证）
2025-07-12 12:25:50,432 - trade_INSUR - INFO - 使用tx_token_change_tracker获取交易 0x045671b16bfb6c8931dcc038f1ca3f02ec7584849d8bbd1db786fe418c27976f 的USDT数量...
2025-07-12 12:25:54,170 - trade_INSUR - INFO - 从tx_token_change_tracker成功获取到USDT数量
2025-07-12 12:25:54,171 - trade_INSUR - INFO - 成功获取到USDT数量: 9.637557
2025-07-12 12:25:54,172 - trade_INSUR - INFO - 最优链卖出交易执行成功:
2025-07-12 12:25:54,172 - trade_INSUR - INFO -   执行链: ethereum
2025-07-12 12:25:54,173 - trade_INSUR - INFO -   交易哈希: 0x045671b16bfb6c8931dcc038f1ca3f02ec7584849d8bbd1db786fe418c27976f
2025-07-12 12:25:54,179 - trade_INSUR - INFO -   实际收到USDT数量: 9.637557
2025-07-12 12:25:54,181 - trade_INSUR - INFO - 卖出记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\sell\INSUR_20250712_122554_sell.json
2025-07-12 12:25:54,183 - trade_INSUR - INFO - 最优链选择策略执行成功
2025-07-12 12:25:54,183 - trade_INSUR - INFO - 卖出操作成功完成
2025-07-12 12:25:54,184 - trade_INSUR - INFO - 交易执行完成，耗时: 3739.82秒
2025-07-12 12:25:54,184 - trade_INSUR - INFO - ================================================================================
