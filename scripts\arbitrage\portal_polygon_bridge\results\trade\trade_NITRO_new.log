2025-07-09 09:30:49,870 - trade_NITRO - INFO - ================================================================================
2025-07-09 09:30:49,871 - trade_NITRO - INFO - 开始执行 NITRO 买入交易 - 时间: 2025-07-09 09:30:49
2025-07-09 09:30:49,872 - trade_NITRO - INFO - 链: ethereum, 投入金额: 37.51 USDT
2025-07-09 09:30:49,873 - trade_NITRO - INFO - 代币地址: ******************************************
2025-07-09 09:30:49,884 - trade_NITRO - INFO - NITRO: 将在ethereum链上执行买入，代币地址: ******************************************
2025-07-09 09:30:49,884 - trade_NITRO - INFO - NITRO: 准备使用KyberSwap在ethereum上执行37.51USDT买入NITRO交易
2025-07-09 09:31:12,997 - trade_NITRO - INFO - NITRO: 使用tx_token_change_tracker获取交易 0x2e1a88bfae24e453a07c02219336baeb0adc4e6f35e448135d3826d4120b5dd2 的代币数量...
2025-07-09 09:31:17,405 - trade_NITRO - INFO - NITRO: 从tx_token_change_tracker成功获取到代币数量: 196197.573172732679160702
2025-07-09 09:31:17,405 - trade_NITRO - INFO - 交易执行成功:
2025-07-09 09:31:17,405 - trade_NITRO - INFO -   交易哈希: 0x2e1a88bfae24e453a07c02219336baeb0adc4e6f35e448135d3826d4120b5dd2
2025-07-09 09:31:17,405 - trade_NITRO - INFO -   实际输出数量: 196197.5731727327 NITRO
2025-07-09 09:31:17,405 - trade_NITRO - INFO - 开始执行桥接操作，方向: ethereum_to_polygon
2025-07-09 09:31:17,405 - trade_NITRO - INFO - ================================================================================
2025-07-09 09:31:17,405 - trade_NITRO - INFO - 开始执行 NITRO 桥接操作 - 时间: 2025-07-09 09:31:17
2025-07-09 09:31:17,405 - trade_NITRO - INFO - 桥接方向: ethereum_to_polygon
2025-07-09 09:31:17,405 - trade_NITRO - INFO - 代币数量: 196197.573172732679160702 NITRO
2025-07-09 09:31:20,527 - trade_NITRO - INFO - 从以太坊桥接到Polygon: 196197.573172732679160702 NITRO
2025-07-09 09:33:06,519 - trade_NITRO - ERROR - 桥接操作失败: 桥接过程出错（已重试3次）: 代币余额不足: 需要 196197.573172732679160702 NITRO，但只有 0.0 NITRO
2025-07-09 09:33:06,520 - trade_NITRO - ERROR - 桥接操作失败: 桥接过程出错（已重试3次）: 代币余额不足: 需要 196197.573172732679160702 NITRO，但只有 0.0 NITRO
2025-07-09 09:33:06,521 - trade_NITRO - INFO - 交易执行完成，耗时: 136.65秒
2025-07-09 09:33:06,526 - trade_NITRO - INFO - ================================================================================
2025-07-12 09:35:08,526 - trade_NITRO - INFO - ================================================================================
2025-07-12 09:35:08,527 - trade_NITRO - INFO - 开始执行 NITRO 买入交易 - 时间: 2025-07-12 09:35:08
2025-07-12 09:35:08,527 - trade_NITRO - INFO - 链: ethereum, 投入金额: 62.51 USDT
2025-07-12 09:35:08,527 - trade_NITRO - INFO - 代币地址: ******************************************
2025-07-12 09:35:08,534 - trade_NITRO - INFO - NITRO: 将在ethereum链上执行买入，代币地址: ******************************************
2025-07-12 09:35:08,534 - trade_NITRO - INFO - NITRO: 准备使用KyberSwap在ethereum上执行62.51USDT买入NITRO交易
2025-07-12 09:36:07,040 - trade_NITRO - INFO - NITRO: 使用tx_token_change_tracker获取交易 0xa10090ef6ddd8f667fd1573b370210cc15354ba473cabbfc9200339439e22bde 的代币数量...
2025-07-12 09:36:10,051 - trade_NITRO - INFO - NITRO: 从tx_token_change_tracker成功获取到代币数量: 282938.214334025360664267
2025-07-12 09:36:10,051 - trade_NITRO - INFO - 交易执行成功:
2025-07-12 09:36:10,051 - trade_NITRO - INFO -   交易哈希: 0xa10090ef6ddd8f667fd1573b370210cc15354ba473cabbfc9200339439e22bde
2025-07-12 09:36:10,051 - trade_NITRO - INFO -   实际输出数量: 282938.21433402534 NITRO
2025-07-12 09:36:10,051 - trade_NITRO - INFO - 开始执行桥接操作，方向: ethereum_to_polygon
2025-07-12 09:36:10,051 - trade_NITRO - INFO - ================================================================================
2025-07-12 09:36:10,051 - trade_NITRO - INFO - 开始执行 NITRO 桥接操作 - 时间: 2025-07-12 09:36:10
2025-07-12 09:36:10,051 - trade_NITRO - INFO - 桥接方向: ethereum_to_polygon
2025-07-12 09:36:10,051 - trade_NITRO - INFO - 代币数量: 282938.214334025360664267 NITRO
2025-07-12 09:36:21,332 - trade_NITRO - INFO - 从以太坊桥接到Polygon: 282938.214334025360664267 NITRO
2025-07-12 09:55:28,563 - trade_NITRO - INFO - 桥接操作成功完成
2025-07-12 09:55:28,564 - trade_NITRO - INFO - 桥接交易哈希: 0x2b81f7679294158788505847ab3df8e509d935ebe9d973a12177d2141fe4560d
2025-07-12 09:55:28,564 - trade_NITRO - INFO - Polygon交易哈希: None
2025-07-12 09:55:28,565 - trade_NITRO - INFO - 桥接记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\bridge\NITRO_20250712_095528_bridge.json
2025-07-12 09:55:28,565 - trade_NITRO - INFO - 桥接操作成功完成
2025-07-12 09:55:28,566 - trade_NITRO - INFO - 开始执行卖出操作...
2025-07-12 09:55:28,566 - trade_NITRO - INFO - ================================================================================
2025-07-12 09:55:28,567 - trade_NITRO - INFO - 开始执行 NITRO 卖出交易 - 时间: 2025-07-12 09:55:28
2025-07-12 09:55:28,572 - trade_NITRO - INFO - 目标链: polygon
2025-07-12 09:55:28,572 - trade_NITRO - INFO - 目标链代币地址: 0x695fc8b80f344411f34bdbcb4e621aa69ada384b
2025-07-12 09:55:28,572 - trade_NITRO - INFO - 在polygon链上执行卖出操作
2025-07-12 09:55:28,572 - trade_NITRO - INFO - 代币地址: 0x695fc8b80f344411f34bdbcb4e621aa69ada384b
2025-07-12 09:55:28,572 - trade_NITRO - INFO - 卖出数量: 282938.214334025360664267 NITRO
2025-07-12 09:55:28,572 - trade_NITRO - INFO - 预期USDT输出: 65.529716 USDT
2025-07-12 09:55:32,430 - trade_NITRO - INFO - 执行卖出交易，预期USDT输出: 65.529716
2025-07-12 09:56:40,626 - trade_NITRO - INFO - 使用tx_token_change_tracker获取交易 0xd4638df08353aa9fcf126a6b620c74d317d90b3c8f6a597b550010002d1cc2fd 的USDT数量...
2025-07-12 09:56:54,017 - trade_NITRO - INFO - 从tx_token_change_tracker成功获取到USDT数量
2025-07-12 09:56:54,017 - trade_NITRO - INFO - 成功获取到USDT数量: 65.246237
2025-07-12 09:56:54,017 - trade_NITRO - INFO - 卖出交易执行成功:
2025-07-12 09:56:54,017 - trade_NITRO - INFO -   交易哈希: 0xd4638df08353aa9fcf126a6b620c74d317d90b3c8f6a597b550010002d1cc2fd
2025-07-12 09:56:54,017 - trade_NITRO - INFO -   实际收到USDT数量: 65.246237
2025-07-12 09:56:54,018 - trade_NITRO - INFO - 卖出记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\sell\NITRO_20250712_095654_sell.json
2025-07-12 09:56:54,018 - trade_NITRO - INFO - 卖出操作成功完成
2025-07-12 09:56:54,018 - trade_NITRO - INFO - 交易执行完成，耗时: 1305.49秒
2025-07-12 09:56:54,018 - trade_NITRO - INFO - ================================================================================
