api_keys:
  arbitrumscan:
    key: XVSMDKH4N95WNBUAV5<PERSON><PERSON>312ZZPSSS7CMG
  basescan:
    key: IEHC6XXK73E4QRD631SW2QUW4NY847S86W
  bscscan:
    key: **********************************
  etherscan:
    key: **********************************
  moralis:
    base_url: https://deep-index.moralis.io/api/v2
    key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJub25jZSI6ImQzNzNkYzMxLTkwOTktNGE4OC1hMmFmLTY3YWJlM2RlMWI2MCIsIm9yZ0lkIjoiNDE0MDYwIiwidXNlcklkIjoiNDI1NTQwIiwidHlwZUlkIjoiMDIxZGJlZjYtNTc4Zi00ZWVmLTkyNzQtZjQ4N2JmMjY3NjgzIiwidHlwZSI6IlBST0pFQ1QiLCJpYXQiOjE3MzAzODY0MjIsImV4cCI6NDg4NjE0NjQyMn0.lUa6DqlDcO8xXaxIEmS3-NJtW2qKHT0RQYmS06F1EOg
  optimismscan:
    key: R394MUMIAZBU2C8E4NVDYBBUB7RWGFN58D
  polygonscan:
    key: GPHHB2FE5KTCCR27A44E89D695N7WUBXCF
  snowtrace:
    key: IG3UNFY34PK6BBK9SEIARR63RAAMA928A1
  tenderly:
    user: "rich2025"
    project: "cex-dex-arb"
    access_key: "eAFrT2pa6UkmCGiSsNOu2SswqnnKpM0O"
    base_url: "https://api.tenderly.co/api/v1"
arbitrage:
  risk_control:
    max_daily_amount: 1000
    max_order_amount: 100
  threshold:
    min_profit_percentage: 0.5
  trading:
    gas_price_multiplier: 1.1
    slippage_tolerance: 0.5
cex:
  gate:
    api_key: 1cd71d24dd77d422c41d8013b5debfd4
    enable_rate_limit: true
    password: ''
    secret_key: 08091f6118fd9c93b20d6429ef0e79de6f4359716346a49c8170b9c15e9aecee
    timeout: 30000

rpc:
  ethereum:
    backup_rpc_urls:
    - https://mainnet.infura.io/v3/********************************
    - https://1rpc.io/eth
    - https://rpc.mevblocker.io
    - https://eth.llamarpc.com
    - https://eth.blockrazor.xyz
    - https://ethereum-rpc.publicnode.com
    rpc_url: https://mainnet.infura.io/v3/********************************
    ws_url: wss://mainnet.infura.io/ws/v3/********************************
  polygon:
    backup_rpc_urls:
    - https://polygon-mainnet.infura.io/v3/********************************
    - https://1rpc.io/matic
    - https://polygon-mainnet.public.blastapi.io
    - https://polygon-rpc.com
    - https://polygon-rpc.publicnode.com
    rpc_url: https://polygon-mainnet.infura.io/v3/********************************
    ws_url: wss://polygon-mainnet.infura.io/ws/v3/********************************

wallet:
  private_key: "0x0fac6d7b84756889018b14b4b2739b75d1ab0ca76cef5c75e25aa3cceba1aba9" 

dex:
  astar:
    rpc:
      backup_endpoints:
      - https://astar-rpc.dwellir.com
      - https://astar.api.onfinality.io/public
      endpoint: https://astar.public.blastapi.io
    wallet:
      private_key: '0x0fac6d7b84756889018b14b4b2739b75d1ab0ca76cef5c75e25aa3cceba1aba9'
  base:
    backup_rpc_urls:
    - https://mainnet.base.org
    - https://base.meowrpc.com
    - https://base-rpc.publicnode.com
    - https://1rpc.io/base
    rpc_url: https://base.llamarpc.com
    tx_settings:
      deadline_seconds: 600
      gas_limit_multiplier: 1.2
      gas_price_multiplier: 1.1
    wallet:
      private_key: '0x0fac6d7b84756889018b14b4b2739b75d1ab0ca76cef5c75e25aa3cceba1aba9'
  bsc:
    backup_rpc_urls:
    - https://bsc.blockrazor.xyz
    - https://bsc.drpc.org
    - https://rpc-bsc.48.club
    - https://bsc-rpc.publicnode.com
    rpc_url: https://binance.llamarpc.com
    tx_settings:
      deadline_seconds: 600
      gas_limit_multiplier: 1.2
      gas_price_multiplier: 1.1
    wallet:
      private_key: '0x0fac6d7b84756889018b14b4b2739b75d1ab0ca76cef5c75e25aa3cceba1aba9'
  ethereum:
    backup_rpc_urls:
    - https://1rpc.io/eth
    - https://rpc.mevblocker.io
    - https://eth.llamarpc.com
    - https://eth.blockrazor.xyz
    rpc_url: https://ethereum-rpc.publicnode.com
    wallet:
      private_key: '0x0fac6d7b84756889018b14b4b2739b75d1ab0ca76cef5c75e25aa3cceba1aba9'
  polygon:
    backup_rpc_urls:
    - https://rpc-mainnet.matic.network
    - https://1rpc.io/matic
    - https://polygon-mainnet.public.blastapi.io
    rpc_url: https://polygon-rpc.com
    tx_settings:
      deadline_seconds: 600
      gas_limit_multiplier: 1.2
      gas_price_multiplier: 1.1
    wallet:
      private_key: '0x0fac6d7b84756889018b14b4b2739b75d1ab0ca76cef5c75e25aa3cceba1aba9'
logging:
  backup_count: 5
  file: logs/arbitrage.log
  level: INFO
  max_size: 10
  rotation: true
proxy:
  proxy_list:
  - socks5://746649bb3e9aa558ccdc:<EMAIL>:824
