#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
持续运行的套利分析主脚本
"""

import os
import sys
import time
import logging
import asyncio
from datetime import datetime
import threading

# 添加项目根目录到系统路径
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_ROOT)

from scripts.utils.get_gas import get_current_gas_price
from scripts.arbitrage.portal_polygon_bridge.run_arbitrage_finder import main as run_finder

# Windows平台异步支持
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("arb_finder_main")

async def check_gas_price() -> bool:
    """
    检查当前gas价格是否低于阈值
    
    Returns:
        bool: 如果gas价格低于2.5 Gwei返回True，否则返回False
    """
    try:
        gas_price = await get_current_gas_price()
        if gas_price is None:
            logger.warning("无法获取gas价格，将继续执行任务")
            return False
            
        logger.info(f"当前ETH gas价格: {gas_price:.2f} Gwei")
        
        if gas_price <= 2.5:
            logger.info(f"Gas价格较低 ({gas_price:.2f} Gwei)，开始执行套利任务")
            return True
        else:
            logger.warning(f"Gas价格过高 ({gas_price:.2f} Gwei > 2.5 Gwei)，跳过本轮任务")
            return False
            
    except Exception as e:
        logger.error(f"检查gas价格时出错: {str(e)}")
        # 如果出错，默认继续执行
        return False

def run_finder_in_thread():
    """
    在新线程中运行套利查找器
    """
    try:
        run_finder()
    except Exception as e:
        logger.error(f"套利查找器执行出错: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
    finally:
        # 标记任务完成
        global current_task_completed
        current_task_completed = True

def main():
    """
    主函数
    """
    global current_task_completed
    current_task_completed = True  # 初始状态为已完成
    
    logger.info("启动持续运行的套利分析...")
    
    while True:
        try:
            # 如果上一轮任务还在运行，等待其完成
            if not current_task_completed:
                logger.info("等待上一轮任务完成...")
                time.sleep(5)  # 每5秒检查一次
                continue
            
            # 检查gas价格
            if not asyncio.run(check_gas_price()):
                # 如果gas价格过高，等待30秒后重试
                wait_time = 30
                logger.info(f"等待 {wait_time} 秒后重新检查gas价格...")
                time.sleep(wait_time)
                continue
            
            # 标记新任务开始
            current_task_completed = False
            
            # 在新线程中运行套利查找器
            finder_thread = threading.Thread(
                target=run_finder_in_thread,
                name="ArbFinder"
            )
            finder_thread.daemon = True  # 设置为守护线程，这样主程序退出时线程会自动结束
            finder_thread.start()
            
            logger.info("新一轮任务已启动，等待完成...")
            
        except KeyboardInterrupt:
            logger.info("用户中断操作")
            break
        except Exception as e:
            logger.error(f"主循环出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            # 出错后等待一段时间再继续
            time.sleep(60)

if __name__ == "__main__":
    main() 