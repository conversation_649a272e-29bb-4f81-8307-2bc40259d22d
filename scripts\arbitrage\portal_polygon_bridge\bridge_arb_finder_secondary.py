#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Polygon和以太坊网络间套利机会二次分析脚本
对发现利润的代币进行更详细的分析
"""

import os
import sys
import json
import asyncio
import time
import logging
import concurrent.futures
import random
import subprocess
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime, timedelta
import pandas as pd
import threading
import numpy as np
import requests
import argparse
import multiprocessing
import queue
import aiohttp
import yaml

# 添加项目根目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))

# 导入主脚本中的常量和日志记录器
from scripts.arbitrage.portal_polygon_bridge.bridge_arb_finder import BridgeArbFinder, RESULTS_DIR, secondary_logger as logger
# 导入KyberSwapClient
from src.dex.KyberSwap.client import KyberSwapClient
# 导入地址验证所需的函数 - 处理在类中

# 确保日志只使用导入的记录器，不创建新的记录器
# 禁用模块级别的根日志记录器传播
root_logger = logging.getLogger()
for handler in root_logger.handlers[:]:
    root_logger.removeHandler(handler)

# 初始化脚本开始
logger.info("=" * 60)
logger.info("开始二次分析套利机会")
logger.info("=" * 60)

def get_token_decimals(token_address: str, chain: str) -> int:
    """
    获取代币精度

    Args:
        token_address: 代币地址
        chain: 链名称 (ethereum/polygon)

    Returns:
        int: 代币精度，默认为18
    """
    try:
        # 从gate_tokens_with_decimals.json读取代币精度
        tokens_file = os.path.join("data", "utils", "token", "gate_tokens_with_decimals.json")
        if os.path.exists(tokens_file):
            with open(tokens_file, "r", encoding="utf-8") as f:
                tokens_data = json.load(f)

            # 使用正确的链键名称
            chain_key = "ethereum" if chain == "ethereum" else "polygon" if chain == "polygon" else None

            if chain_key and chain_key in tokens_data:
                # 遍历该链上的所有代币
                for token_info in tokens_data[chain_key]:
                    if token_info.get("contract_address", "").lower() == token_address.lower():
                        decimals = token_info.get("decimals")
                        if decimals is not None:
                            logger.info(f"从gate_tokens_with_decimals.json读取到代币精度: {decimals}")
                            return decimals

        # 如果无法获取到精度，返回默认值18
        return 18
    except Exception as e:
        logger.error(f"获取代币精度失败: {str(e)}")
        return 18

# 定义验证后的套利机会文件路径
VERIFIED_OPPS_FILE = os.path.join(RESULTS_DIR, "verified_opportunities.json")
VERIFIED_OPPS_CSV = os.path.join(RESULTS_DIR, "verified_opportunities.csv")

class SecondaryArbAnalyzer:
    def __init__(self, num_workers: int = 10):
        """
        初始化二次分析器
        
        Args:
            num_workers: 并行工作线程数量
        """
        self.num_workers = num_workers
        self.opportunities = []
        self.verified_opportunities = []  # 存储验证过的套利机会
        self.lock = threading.Lock()
        
        # 创建主分析器实例，使用10个线程
        self.arb_finder = BridgeArbFinder(num_workers=10)  # 使用10个线程进行并行分析
        
        # 创建KyberSwap客户端实例，用于模拟交易 - 不需要私钥
        self.ethereum_client = KyberSwapClient(chain="ethereum") # 模拟交易不需要私钥
        self.polygon_client = KyberSwapClient(chain="polygon") # 模拟交易不需要私钥
        logger.info("已创建KyberSwap客户端实例，用于模拟交易")
        
        # 代理相关配置
        self.proxy_failure_count = {}  # 记录每个代理的失败次数
        self.max_proxy_failures = 3  # 最大失败次数
        self.current_proxy_index = 0
        self.proxy_lock = threading.Lock()
        
        # 从配置文件加载代理列表
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'config', 'config.yaml')
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
                self.socks5_proxies = config.get('proxy', {}).get('proxy_list', [])
        else:
            self.socks5_proxies = []
            
        logger.info(f"已加载 {len(self.socks5_proxies)} 个SOCKS5代理")
        
    def get_random_socks5_proxy(self) -> Optional[str]:
        """获取一个可用的SOCKS5代理"""
        with self.proxy_lock:
            if not self.socks5_proxies:
                return None
                
            # 获取当前代理
            proxy = self.socks5_proxies[self.current_proxy_index]
            
            # 检查代理是否可用
            if self.proxy_failure_count.get(proxy, 0) >= self.max_proxy_failures:
                # 如果当前代理失败次数过多，尝试下一个
                self.current_proxy_index = (self.current_proxy_index + 1) % len(self.socks5_proxies)
                proxy = self.socks5_proxies[self.current_proxy_index]
                self.proxy_failure_count[proxy] = 0
                logger.info(f"切换到新代理: {proxy}")
            
            return proxy
            
    def mark_proxy_failure(self, proxy: str):
        """标记代理失败"""
        with self.proxy_lock:
            self.proxy_failure_count[proxy] = self.proxy_failure_count.get(proxy, 0) + 1
            if self.proxy_failure_count[proxy] >= self.max_proxy_failures:
                logger.warning(f"代理 {proxy} 失败次数过多，将被暂时禁用")
                # 自动切换到下一个代理
                self.current_proxy_index = (self.current_proxy_index + 1) % len(self.socks5_proxies)
                next_proxy = self.socks5_proxies[self.current_proxy_index]
                logger.info(f"自动切换到新代理: {next_proxy}")

    def mark_proxy_success(self, proxy: str):
        """标记代理成功"""
        with self.proxy_lock:
            self.proxy_failure_count[proxy] = 0
            
    async def rotate_proxies(self):
        """定期轮换代理"""
        while True:
            try:
                await asyncio.sleep(self.proxy_rotation_interval)
                with self.proxy_lock:
                    self.current_proxy_index = (self.current_proxy_index + 1) % len(self.socks5_proxies)
                    logger.info(f"定期轮换到新代理: {self.socks5_proxies[self.current_proxy_index]}")
            except Exception as e:
                logger.error(f"轮换代理时发生错误: {str(e)}")

    async def _verify_addresses_async(self, opportunity: Dict) -> Dict:
        """
        异步验证交易地址，使用线程池执行，确保不阻塞其他任务
        
        Args:
            opportunity: 套利机会信息，必须包含bridge_direction
            
        Returns:
            Dict: 验证结果
        """
        symbol = opportunity.get('symbol', 'Unknown')
        logger.info(f"开始异步验证 {symbol} 的交易地址")
        
        try:
            # 确保bridge_direction存在
            if 'bridge_direction' not in opportunity or not opportunity['bridge_direction']:
                logger.warning(f"{symbol} 未指定交易方向，无法验证")
                return {
                    "address_verified": False, 
                    "reason": "未指定交易方向",
                    "symbol": symbol
                }
            
            # 记录交易方向
            bridge_direction = opportunity['bridge_direction']
            logger.info(f"{symbol} 的交易方向为: {bridge_direction}")
            
            # 使用线程池执行验证任务，但不等待结果
            loop = asyncio.get_running_loop()
            with concurrent.futures.ThreadPoolExecutor() as executor:
                # 提交验证任务到线程池
                future = executor.submit(self.verify_transaction_addresses, opportunity)
                
                # 使用loop.run_in_executor将future.result()转换为协程
                try:
                    # 设置超时时间，避免永久阻塞
                    result = await asyncio.wait_for(
                        loop.run_in_executor(None, lambda: future.result(timeout=60)),
                        timeout=65
                    )
                    
                    # 记录验证结果
                    if result.get('address_verified', False):
                        logger.info(f"{symbol} 地址验证结果: 通过")
                    else:
                        reason = result.get('reason', '未知原因')
                        logger.warning(f"{symbol} 地址验证结果: 未通过 - {reason}")
                    
                    return result
                except concurrent.futures.TimeoutError:
                    logger.error(f"{symbol} 地址验证超时")
                    return {
                        "address_verified": False, 
                        "reason": "验证超时",
                        "symbol": symbol
                    }
                except Exception as e:
                    logger.error(f"{symbol} 地址验证出错: {str(e)}")
                    return {
                        "address_verified": False, 
                        "reason": f"验证出错: {str(e)}",
                        "symbol": symbol
                    }
        except Exception as e:
            logger.error(f"异步验证 {symbol} 地址时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return {
                "address_verified": False, 
                "reason": f"验证过程出错: {str(e)}",
                "symbol": symbol
            }
    
    def verify_transaction_addresses(self, opportunity: Dict) -> Dict:
        """
        验证交易地址是否有最近的交易记录
        
        Args:
            opportunity: 套利机会信息，包含交易方向和地址信息
            
        Returns:
            Dict: 验证结果，包含address_verified字段
        """
        try:
            # 获取必要信息
            symbol = opportunity.get('symbol', 'Unknown')
            polygon_address = opportunity.get('polygon_address', '')
            eth_address = opportunity.get('eth_address', '')
            bridge_direction = opportunity.get('bridge_direction', '')
            
            logger.info(f"开始验证 {symbol} 的交易地址，交易方向: {bridge_direction}")
            
            if not bridge_direction:
                logger.warning(f"{symbol} 未指定交易方向，无法验证")
                return {
                    "address_verified": False, 
                    "reason": "未指定交易方向",
                    "symbol": symbol
                }
            
            # 根据桥的方向确定检查哪个链上的交易
            if bridge_direction == "ethereum_to_polygon":
                # 当方向是从以太坊到Polygon时，检查以太坊上的交易
                check_chain = "ethereum"
                check_minutes = 30  # 检查30分钟内的交易
                check_address = eth_address
                logger.info(f"检查 {symbol} 在以太坊上最近 {check_minutes} 分钟内的交易")
            elif bridge_direction == "polygon_to_ethereum":
                # 当方向是从Polygon到以太坊时，检查Polygon上的交易
                check_chain = "polygon"
                check_minutes = 120  # 检查120分钟内的交易
                check_address = polygon_address
                logger.info(f"检查 {symbol} 在Polygon上最近 {check_minutes} 分钟内的交易")
            else:
                logger.warning(f"{symbol} 未知的交易方向: {bridge_direction}")
                return {
                    "address_verified": False, 
                    "reason": f"未知的交易方向: {bridge_direction}",
                    "symbol": symbol
                }
            
            try:
                # 导入monitor_token_tx模块
                import importlib.util
                spec = importlib.util.spec_from_file_location(
                    "monitor_token_tx", 
                    os.path.join(os.path.dirname(__file__), "monitor_token_tx.py")
                )
                monitor_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(monitor_module)
                
                # 使用monitor_token_tx的get_token_transactions函数获取交易
                logger.info(f"使用RPC获取 {symbol} 在 {check_chain} 上的最近{check_minutes}分钟交易记录")
                
                # 创建Web3实例
                web3 = monitor_module.get_web3_client(check_chain)
                if not isinstance(web3, list):
                    web3 = [web3]  # 确保是列表形式
                
                # 尝试获取交易记录
                transactions = None
                for rpc in web3:
                    try:
                        transactions = monitor_module.get_token_transactions(check_address, check_chain, check_minutes)
                        if transactions is not None:  # 成功获取交易记录
                            break
                    except Exception as e:
                        logger.warning(f"使用RPC {rpc} 获取交易记录失败: {str(e)}")
                        continue
                
                # 如果所有RPC都失败
                if transactions is None:
                    logger.error(f"{symbol} 在 {check_chain} 上获取交易记录失败")
                    return {
                        "address_verified": False,
                        "reason": "无法获取交易记录",
                        "symbol": symbol
                    }
                
                # 如果没有找到任何交易记录，这是好事
                if not transactions:
                    logger.info(f"{symbol} 在 {check_chain} 上没有找到任何交易记录，这是好事 - 验证通过")
                    return {
                        "address_verified": True, 
                        "reason": f"在{check_chain}上没有找到任何交易记录",
                        "symbol": symbol
                    }
                
                # 检查是否有目标地址的交易
                target_address = "******************************************" if check_chain == "ethereum" else "******************************************"
                target_address_lower = target_address.lower()
                
                target_txs = [tx for tx in transactions if tx.get('to', '').lower() == target_address_lower]
                
                if target_txs:
                    logger.warning(f"{symbol} 在 {check_chain} 上找到 {len(target_txs)} 条接收地址为 {target_address} 的交易 - 验证失败")
                    return {
                        "address_verified": False,
                        "reason": f"找到{len(target_txs)}条接收地址为{target_address}的交易",
                        "symbol": symbol
                    }
                else:
                    logger.info(f"{symbol} 在 {check_chain} 上没有找到接收地址为 {target_address} 的交易 - 验证通过")
                    return {
                        "address_verified": True,
                        "reason": f"没有找到接收地址为{target_address}的交易",
                        "symbol": symbol
                    }
                
            except Exception as e:
                logger.error(f"验证 {symbol} 的交易记录时出错: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
                return {
                    "address_verified": False,
                    "reason": f"验证过程出错: {str(e)}",
                    "symbol": symbol
                }
            
        except Exception as e:
            logger.error(f"验证 {opportunity.get('symbol', 'unknown')} 的交易地址时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return {
                "address_verified": False,
                "reason": f"验证过程出错: {str(e)}",
                "symbol": opportunity.get('symbol', 'unknown')
            }
    
    def verify_profit_conditions(self, opportunity: Dict) -> bool:
        """
        验证利润是否满足条件
        
        Args:
            opportunity: 套利机会
            
        Returns:
            bool: 是否满足条件
        """
        try:
            # 检查利润是否大于 1 USDT
            profit_above_1usdt = opportunity.get('profit_above_1usdt', False)
            
            # 检查利润是否大于 0.5 倍 gas 成本
            profit_above_half_gas = opportunity.get('profit_above_half_gas', False)
            
            # 满足条件: 利润大于1 USDT 且 大于 0.5 倍 gas 成本
            meets_conditions = profit_above_1usdt and profit_above_half_gas
            
            if meets_conditions:
                logger.info(f"{opportunity.get('symbol', 'unknown')} 满足利润条件: "
                            f"利润 > 1 USDT: {profit_above_1usdt}, "
                            f"利润 > 0.5倍gas: {profit_above_half_gas}")
            else:
                logger.warning(f"{opportunity.get('symbol', 'unknown')} 不满足利润条件: "
                              f"利润 > 1 USDT: {profit_above_1usdt}, "
                              f"利润 > 0.5倍gas: {profit_above_half_gas}")
            
            return meets_conditions
            
        except Exception as e:
            logger.error(f"验证 {opportunity.get('symbol', 'unknown')} 的利润条件时出错: {str(e)}")
            return False
    
    async def get_profitable_tokens(self) -> List[Dict]:
        """
        获取最近10分钟内有利润的代币
        
        Returns:
            List[Dict]: 代币信息列表
        """
        csv_file = os.path.join(RESULTS_DIR, "arbitrage_opportunities.csv")
        json_file = os.path.join(RESULTS_DIR, "arbitrage_opportunities.json")
        
        if os.path.exists(json_file):
            try:
                # 优先从JSON文件读取，因为它包含完整的bridge_direction信息
                with open(json_file, 'r', encoding='utf-8') as f:
                    all_opportunities = json.load(f)
                logger.info(f"从JSON文件加载了 {len(all_opportunities)} 条记录")
                
                # 获取当前时间
                current_time = datetime.now()
                
                # 筛选最近10分钟内的记录
                recent_opps = []
                for opp in all_opportunities:
                    if 'time' in opp:
                        try:
                            opp_time = datetime.strptime(opp['time'], "%Y-%m-%d %H:%M:%S")
                            if (current_time - opp_time).total_seconds() <= 600:  # 10分钟 = 600秒
                                recent_opps.append(opp)
                        except Exception:
                            pass
                
                logger.info(f"筛选出最近10分钟内的 {len(recent_opps)} 条记录")
                
                # 筛选有利润的记录
                profitable_opps = [opp for opp in recent_opps if opp.get('net_profit', 0) > 0]
                logger.info(f"其中有 {len(profitable_opps)} 条记录有正利润")
                
                # 按代币分组，获取每个代币的最优记录
                token_groups = {}
                for opp in profitable_opps:
                    symbol = opp.get('symbol')
                    if symbol not in token_groups or opp.get('net_profit', 0) > token_groups[symbol].get('net_profit', 0):
                        token_groups[symbol] = opp
                
                tokens_to_analyze = []
                for symbol, best_opp in token_groups.items():
                    # 获取代币数据
                    token_data = self.arb_finder.tokens_data.get(best_opp.get('polygon_address', ''))
                    if token_data:
                        # 必须字段检查
                        if 'token_amount' not in best_opp or best_opp['token_amount'] <= 0:
                            logger.error(f"{symbol}: JSON记录中没有token_amount字段或值为0，无法进行交易验证")
                            continue
                            
                        bridge_direction = best_opp.get('bridge_direction', '')
                        if not bridge_direction:
                            # 从价格差异确定方向
                            eth_price = best_opp.get('eth_price', 0)
                            polygon_price = best_opp.get('polygon_price', 0)
                            
                            if eth_price > polygon_price:
                                bridge_direction = 'polygon_to_ethereum'
                                logger.info(f"{symbol}: 以太坊价格({eth_price})>Polygon价格({polygon_price})，方向为polygon_to_ethereum")
                            else:
                                bridge_direction = 'ethereum_to_polygon'
                                logger.info(f"{symbol}: Polygon价格({polygon_price})>=以太坊价格({eth_price})，方向为ethereum_to_polygon")
                        else:
                            logger.info(f"{symbol}: 从JSON记录中获取交易方向: {bridge_direction}")
                        
                        # 添加方向到token_data
                        token_data['bridge_direction'] = bridge_direction
                        
                        # 直接从记录中获取token_amount - 初始分析已经计算过了
                        token_amount = best_opp['token_amount']
                        logger.info(f"{symbol}: 直接从初次分析中获取代币数量: {token_amount}")
                        
                        # 记录关键信息
                        logger.info(f"{symbol}: 代币数量={token_amount}, 方向={bridge_direction}, USDT输入={best_opp.get('usdt_input')}")
                        
                        tokens_to_analyze.append({
                            'symbol': symbol,
                            'name': best_opp.get('name', 'Unknown'),
                            'polygon_address': best_opp.get('polygon_address', ''),
                            'eth_address': best_opp.get('eth_address', ''),
                            'optimal_usdt': best_opp.get('usdt_input', 10),
                            'token_data': token_data,
                            'bridge_direction': bridge_direction,  # 明确设置bridge_direction
                            'token_amount': token_amount,  # 直接使用初次分析的代币数量
                            'eth_price': best_opp.get('eth_price', 0),
                            'polygon_price': best_opp.get('polygon_price', 0),
                            'expected_usdt_out': best_opp.get('usdt_output', 0)  # 添加预期USDT输出
                        })
                
                if tokens_to_analyze:
                    return tokens_to_analyze
                logger.warning("JSON文件中没有找到有效的代币记录，尝试从CSV文件获取")
                
            except Exception as e:
                logger.error(f"从JSON文件获取有利润代币时出错: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
        
        # 如果JSON文件不存在或读取失败，尝试从CSV文件读取
        if os.path.exists(csv_file):
            try:
                # 读取CSV文件
                df = pd.read_csv(csv_file)
                
                # 检查是否包含必要的字段
                required_fields = ['symbol', 'polygon_address', 'eth_address', 'token_amount', 'usdt_input', 'net_profit']
                if not all(field in df.columns for field in required_fields):
                    missing = [field for field in required_fields if field not in df.columns]
                    logger.error(f"CSV文件缺少必要字段: {missing}，无法进行分析")
                    return []
                
                # 获取当前时间
                current_time = pd.Timestamp.now()
                
                # 将time列转换为datetime
                df['time'] = pd.to_datetime(df['time'])
                
                # 筛选最近10分钟内的记录
                recent_df = df[df['time'] > (current_time - pd.Timedelta(minutes=10))]
                
                # 筛选有利润的记录
                profitable_df = recent_df[recent_df['net_profit'] > 0]
                
                # 筛选有token_amount字段的记录
                profitable_df = profitable_df[profitable_df['token_amount'] > 0]
                
                if profitable_df.empty:
                    logger.warning("CSV文件中没有找到满足条件的代币记录")
                    return []
                
                # 按代币分组，获取每个代币的最优记录
                token_groups = profitable_df.groupby('symbol')
                tokens_to_analyze = []
                
                for symbol, group in token_groups:
                    # 获取最优记录（净利润最高的）
                    best_record = group.loc[group['net_profit'].idxmax()]
                    
                    # 获取代币数据
                    token_data = self.arb_finder.tokens_data.get(best_record['polygon_address'])
                    if token_data:
                        # 确定交易方向
                        bridge_direction = best_record.get('bridge_direction', '')
                        if not bridge_direction:
                            # 从价格差异确定方向
                            eth_price = best_record.get('eth_price', 0)
                            polygon_price = best_record.get('polygon_price', 0)
                            
                            if eth_price > polygon_price:
                                bridge_direction = 'ethereum_to_polygon'
                                logger.info(f"{symbol}: 以太坊价格({eth_price})>Polygon价格({polygon_price})，方向为ethereum_to_polygon")
                            else:
                                bridge_direction = 'polygon_to_ethereum'
                                logger.info(f"{symbol}: Polygon价格({polygon_price})>=以太坊价格({eth_price})，方向为polygon_to_ethereum")
                        else:
                            logger.info(f"{symbol}: 从CSV记录中获取交易方向: {bridge_direction}")
                        
                        # 添加方向到token_data
                        token_data['bridge_direction'] = bridge_direction
                        
                        # 直接从记录中获取token_amount - 这是关键部分
                        token_amount = best_record['token_amount']
                        logger.info(f"{symbol}: 直接从CSV记录中获取代币数量: {token_amount}")
                        
                        tokens_to_analyze.append({
                            'symbol': symbol,
                            'name': best_record['name'],
                            'polygon_address': best_record['polygon_address'],
                            'eth_address': best_record['eth_address'],
                            'optimal_usdt': best_record['usdt_input'],
                            'token_data': token_data,
                            'bridge_direction': bridge_direction,  # 明确设置bridge_direction
                            'token_amount': token_amount,  # 明确设置token_amount
                            'expected_usdt_out': best_record.get('usdt_output', 0)  # 添加预期USDT输出
                        })
                
                return tokens_to_analyze
                
            except Exception as e:
                logger.error(f"从CSV文件获取有利润代币时出错: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
        
        return []
    
    def generate_test_amounts(self, optimal_usdt: float) -> List[float]:
        """
        生成测试金额列表
        
        Args:
            optimal_usdt: 最优USDT输入值
            
        Returns:
            List[float]: 测试金额列表
        """
        # 获取与初次分析相同的USDT额度限制
        min_amount = max(self.arb_finder.min_usdt_amount, optimal_usdt * 0.5)
        max_amount = min(self.arb_finder.max_usdt_amount, optimal_usdt * 2.0)
        
        logger.info(f"生成测试金额范围: {min_amount} - {max_amount} USDT (原始范围: {optimal_usdt*0.5} - {optimal_usdt*2.0} USDT)")
        
        # 生成10个等分点
        amounts = np.linspace(min_amount, max_amount, 10)
        
        # 四舍五入到2位小数
        return [round(amount, 2) for amount in amounts]
    
    async def simulate_swap_transaction(self, chain: str, token_in: str, token_out: str, amount: float, **kwargs) -> Dict:
        """
        模拟交易，直接调用KyberSwapClient进行模拟交易

        Args:
            chain: 链名称 (ethereum 或 polygon)
            token_in: 输入代币地址
            token_out: 输出代币地址
            amount: 代币数量
            **kwargs: 额外参数，包括验证信息

        Returns:
            Dict: 模拟结果
        """
        try:
            # 获取验证信息
            is_sell_transaction = kwargs.get('is_sell_transaction', False)
            expected_token_amount = kwargs.get('expected_token_amount', 0)
            symbol = kwargs.get('symbol', 'Unknown')

            logger.info(f"开始在 {chain} 上模拟交易: {amount} {token_in} -> {token_out}")

            # 如果是卖出交易，进行强制验证
            if is_sell_transaction and expected_token_amount > 0:
                logger.info(f"🔍 {symbol} 卖出交易验证:")
                logger.info(f"  传入的amount: {amount}")
                logger.info(f"  预期的token_amount: {expected_token_amount}")

                # 检查是否使用了正确的数量
                if abs(amount - expected_token_amount) > expected_token_amount * 0.01:  # 允许1%的误差
                    logger.error(f"🚨 严重错误：卖出交易使用了错误的数量！")
                    logger.error(f"   传入amount: {amount}")
                    logger.error(f"   应该使用: {expected_token_amount}")
                    logger.error(f"   差异: {abs(amount - expected_token_amount):.6f}")

                    # 强制使用正确的数量
                    logger.error(f"   🔧 强制修正为正确的token_amount: {expected_token_amount}")
                    amount = expected_token_amount
                else:
                    logger.info(f"✅ 卖出交易使用了正确的数量: {amount}")

            logger.info(f"最终使用的数量: {amount}")
            
            # 获取正确的客户端
            client = self.ethereum_client if chain.lower() == "ethereum" else self.polygon_client
            
            # 获取代币精度和原生代币标志
            token_in_decimals = get_token_decimals(token_in, chain)
            
            # 计算wei值
            amount_in_wei = str(int(amount * (10 ** token_in_decimals)))
            
            # 判断是否为原生代币
            is_native_in = False
            if chain.lower() == "ethereum" and token_in.lower() == "******************************************":
                is_native_in = True
            elif chain.lower() == "polygon" and token_in.lower() == "******************************************":
                is_native_in = True
            
            # 记录详细请求信息
            logger.info(f"使用KyberSwapClient进行模拟交易")
            logger.info(f"链: {chain}")
            logger.info(f"输入代币: {token_in}")
            logger.info(f"输出代币: {token_out}")
            logger.info(f"输入金额: {amount} ({amount_in_wei} wei)")
            logger.info(f"是否原生代币: {is_native_in}")
            
            # 获取交易路由
            routes_data = await client.get_routes(
                token_in=token_in,
                token_out=token_out,
                amount_in=amount_in_wei,
                slippage=0.5,  # 0.5%滑点
                is_native_in=is_native_in,
                save_gas=False
            )
            
            if "error" in routes_data:
                error_msg = routes_data["error"]
                logger.error(f"获取交易路由失败: {error_msg}")
                return {
                    "success": False,
                    "reason": f"获取交易路由失败: {error_msg}",
                    "chain": chain
                }
            
            # 验证路由数据格式
            if "data" not in routes_data or "routeSummary" not in routes_data["data"]:
                logger.error("路由数据格式不正确")
                return {
                    "success": False, 
                    "reason": "路由数据格式不正确",
                    "chain": chain
                }
            
            # 获取路由摘要
            route_summary = routes_data["data"]["routeSummary"]
            
            # 从路由中获取信息
            if "amountOut" not in route_summary:
                logger.error("路由摘要中缺少amountOut字段")
                return {
                    "success": False,
                    "reason": "路由摘要中缺少amountOut字段",
                    "chain": chain
                }
            
            # 获取输出代币精度
            token_out_decimals = get_token_decimals(token_out, chain)
            
            # 计算输出金额和Gas成本
            output_amount = int(route_summary["amountOut"])
            output_formatted = output_amount / (10 ** token_out_decimals)
            gas_cost_usd = float(route_summary.get("gasUsd", 0))
            
            # 获取编码路由数据 - 模拟模式下进行测试
            sender = "0x1234567890123456789012345678901234567890"  # 使用一个有效的地址而不是零地址
            recipient = "0x1234567890123456789012345678901234567890"  # 使用一个有效的地址而不是零地址
            deadline = int(time.time()) + 60  # 60秒后过期
            
            # 调用encode_route方法编码路由数据
            encoded_data = await client.encode_route(
                routes_data=routes_data,
                sender=sender,
                recipient=recipient,
                slippage_tolerance=0.5,  # 0.5%滑点
                deadline=deadline,
                source="kyberswap-api-client"  # 客户端标识
            )
            
            if "error" in encoded_data:
                error_msg = encoded_data["error"]
                logger.error(f"编码路由数据失败: {error_msg}")
                return {
                    "success": False,
                    "reason": f"编码路由数据失败: {error_msg}",
                    "chain": chain
                }
            
            # 模拟交易成功
            logger.info(f"模拟交易成功:")
            logger.info(f"  输入: {amount} {token_in}")
            logger.info(f"  输出数量: {output_formatted} {token_out}")
            logger.info(f"  Gas成本: {gas_cost_usd} USD")
            
            # 返回模拟结果
            return {
                "success": True,
                "chain": chain,
                "amount_out": output_formatted,
                "gas_cost_usd": gas_cost_usd,
                "route_info": route_summary.get("route", [])
            }
        
        except Exception as e:
            logger.error(f"模拟交易时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return {
                "success": False,
                "reason": f"模拟交易时出错: {str(e)}",
                "chain": chain
            }

    async def verify_arbitrage_path(self, token_info: Dict) -> Dict:
        """
        验证套利路径，通过模拟交易验证买入和卖出
        
        Args:
            token_info: 代币信息
            
        Returns:
            Dict: 验证结果
        """
        symbol = token_info.get('symbol', 'Unknown')
        polygon_address = token_info.get('polygon_address', '')
        eth_address = token_info.get('eth_address', '')
        usdt_amount = token_info.get('optimal_usdt', 10)
        bridge_direction = token_info.get('bridge_direction', '')
        
        # 严格获取token_amount - 这是必须的关键参数
        if 'token_amount' not in token_info or token_info['token_amount'] <= 0:
            logger.error(f"{symbol}: 严重错误 - 缺少必要的代币数量信息，无法进行模拟交易验证")
            return {
                "verification_success": False,
                "reason": "缺少必要的代币数量信息",
                "symbol": symbol
            }
        
        token_amount = token_info['token_amount']
        logger.info(f"{symbol}: 使用代币数量 {token_amount} 进行模拟交易验证")
        
        # 确保bridge_direction正确获取
        if not bridge_direction and 'token_data' in token_info:
            bridge_direction = token_info['token_data'].get('bridge_direction', '')
        
        if not bridge_direction:
            logger.warning(f"{symbol} 未指定交易方向，无法验证套利路径")
            return {
                "verification_success": False,
                "reason": "未指定交易方向",
                "symbol": symbol
            }
        
        logger.info(f"开始验证 {symbol} 的套利路径，方向: {bridge_direction}, USDT金额: {usdt_amount}, 代币数量: {token_amount}")
        
        try:
            # 获取USDT地址
            eth_usdt = "******************************************"  # 以太坊USDT地址
            polygon_usdt = "******************************************"  # Polygon USDT地址
            
            # 根据桥的方向确定买入和卖出的链
            if bridge_direction == "ethereum_to_polygon":
                buy_chain = "ethereum"
                sell_chain = "polygon"
                buy_token_in = eth_usdt
                buy_token_out = eth_address
                sell_token_in = polygon_address
                sell_token_out = polygon_usdt
                logger.info(f"{symbol} 验证交易路径: 在以太坊上买入 -> 通过桥转到Polygon -> 在Polygon上卖出")
            elif bridge_direction == "polygon_to_ethereum":
                buy_chain = "polygon"
                sell_chain = "ethereum"
                buy_token_in = polygon_usdt
                buy_token_out = polygon_address
                sell_token_in = eth_address
                sell_token_out = eth_usdt
                logger.info(f"{symbol} 验证交易路径: 在Polygon上买入 -> 通过桥转到以太坊 -> 在以太坊上卖出")
            else:
                logger.warning(f"{symbol} 未知的交易方向: {bridge_direction}")
                return {
                    "verification_success": False,
                    "reason": f"未知的交易方向: {bridge_direction}",
                    "symbol": symbol
                }
            
            # 记录要使用的金额
            logger.info(f"买入使用USDT金额: {usdt_amount}")
            logger.info(f"卖出使用代币金额: {token_amount}")
            
            # 使用线程池实现真正的并行执行
            import concurrent.futures
            
            # 创建一个事件循环
            loop = asyncio.get_event_loop()
            
            # 准备买入和卖出的参数
            buy_params = {
                "chain": buy_chain,
                "token_in": buy_token_in,
                "token_out": buy_token_out,
                "amount": usdt_amount
            }
            
            # 强制使用原始token_amount，避免并发问题
            original_token_amount = float(token_amount)  # 创建副本

            # 验证token_amount的合理性
            if original_token_amount <= 0:
                logger.error(f"{symbol} token_amount无效: {original_token_amount}")
                return {
                    "verification_success": False,
                    "reason": f"token_amount无效: {original_token_amount}",
                    "symbol": symbol
                }

            sell_params = {
                "chain": sell_chain,
                "token_in": sell_token_in,
                "token_out": sell_token_out,
                "amount": original_token_amount  # 使用副本确保不被修改
            }

            # 严格验证卖出参数
            logger.info(f"🔍 {symbol} 卖出参数验证:")
            logger.info(f"  sell_chain: {sell_chain}")
            logger.info(f"  sell_token_in: {sell_token_in}")
            logger.info(f"  sell_token_out: {sell_token_out}")
            logger.info(f"  amount (token_amount): {original_token_amount}")
            logger.info(f"  ⚠️  绝对不能使用buy_amount_out进行卖出模拟！")

            # 添加详细日志来追踪参数
            logger.info(f"{symbol} 模拟交易参数:")
            logger.info(f"  买入参数: {buy_params}")
            logger.info(f"  卖出参数: {sell_params}")
            logger.info(f"  原始token_amount: {token_amount}")
            logger.info(f"  卖出使用的amount: {original_token_amount}")
            logger.info(f"  🔍 确保卖出模拟使用原始token_amount而不是buy_amount_out")

            # 创建并发任务，同时模拟买入和卖出 - 使用线程池实现真正的并行
            logger.info(f"使用线程池创建真正并行的 {symbol} 买入和卖出模拟任务")
            
            # 定义包装函数，在独立线程中执行协程
            def run_in_thread(coro_func, params):
                # 创建一个新的事件循环
                new_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(new_loop)
                try:
                    # 执行协程
                    return new_loop.run_until_complete(coro_func(**params))
                finally:
                    new_loop.close()
            
            # 使用线程池同时执行买入和卖出
            buy_result = None
            sell_result = None
            with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
                # 同时提交买入和卖出任务
                buy_future = executor.submit(run_in_thread, self.simulate_swap_transaction, **buy_params)

                # 为卖出任务添加验证信息
                sell_params_with_validation = sell_params.copy()
                sell_params_with_validation.update({
                    'is_sell_transaction': True,
                    'expected_token_amount': original_token_amount,
                    'symbol': symbol
                })
                sell_future = executor.submit(run_in_thread, self.simulate_swap_transaction, **sell_params_with_validation)
                
                # 获取结果 - 这里不会阻塞
                try:
                    buy_result = buy_future.result(timeout=60)
                except concurrent.futures.TimeoutError:
                    logger.error(f"{symbol} 买入模拟超时")
                    buy_result = {
                        "success": False,
                        "reason": "买入模拟超时",
                        "chain": buy_chain
                    }
                
                try:
                    sell_result = sell_future.result(timeout=60)
                except concurrent.futures.TimeoutError:
                    logger.error(f"{symbol} 卖出模拟超时")
                    sell_result = {
                        "success": False,
                        "reason": "卖出模拟超时",
                        "chain": sell_chain
                    }
            
            # 检查买入结果
            buy_success = buy_result.get('success', False)
            buy_reason = buy_result.get('reason', '未知原因')
            
            # 检查卖出结果
            sell_success = sell_result.get('success', False)
            sell_reason = sell_result.get('reason', '未知原因')
            
            # 模拟交易验证成功的条件：买入和卖出都成功
            verification_success = buy_success and sell_success

            # 确定失败原因
            if not buy_success:
                logger.warning(f"{symbol} 买入模拟失败: {buy_reason}")
                reason = f"买入模拟失败: {buy_reason}"
            elif not sell_success:
                logger.warning(f"{symbol} 卖出模拟失败: {sell_reason}")
                reason = f"卖出模拟失败: {sell_reason}"
            else:
                logger.info(f"{symbol} 买入和卖出模拟都成功")
                reason = "模拟交易验证成功"
            
            # 附加更多详细信息
            buy_amount_out = buy_result.get('amount_out', 0)
            sell_amount_out = sell_result.get('amount_out', 0)
            buy_gas_cost = buy_result.get('gas_cost_usd', 0)
            sell_gas_cost = sell_result.get('gas_cost_usd', 0)

            logger.info(f"买入: 输入 {usdt_amount} USDT, 获得 {buy_amount_out} {symbol}, Gas成本: ${buy_gas_cost}")
            logger.info(f"卖出: 输入 {original_token_amount} {symbol}, 获得 {sell_amount_out} USDT, Gas成本: ${sell_gas_cost}")

            # 验证卖出结果的合理性
            logger.info(f"🔍 {symbol} 卖出结果验证:")
            logger.info(f"  预期使用的token_amount: {original_token_amount}")
            logger.info(f"  实际buy_amount_out: {buy_amount_out}")
            logger.info(f"  卖出得到的USDT: {sell_amount_out}")

            # 检查sell_amount_out是否基于正确的token_amount计算
            if original_token_amount > 0 and sell_amount_out > 0:
                price_per_token_from_sell = sell_amount_out / original_token_amount
                logger.info(f"  基于token_amount的单价: {price_per_token_from_sell:.8f} USDT per {symbol}")

                # 如果buy_amount_out和token_amount差异很大，检查sell_amount_out更接近哪个
                if abs(buy_amount_out - original_token_amount) > original_token_amount * 0.05:  # 差异超过5%
                    expected_sell_from_buy = buy_amount_out * price_per_token_from_sell
                    logger.warning(f"  ⚠️  buy_amount_out与token_amount差异较大:")
                    logger.warning(f"     buy_amount_out: {buy_amount_out}")
                    logger.warning(f"     token_amount: {original_token_amount}")
                    logger.warning(f"     如果基于buy_amount_out计算应得: {expected_sell_from_buy:.6f} USDT")
                    logger.warning(f"     实际sell_amount_out: {sell_amount_out} USDT")

                    # 检查哪个更接近
                    diff_to_buy_based = abs(sell_amount_out - expected_sell_from_buy)
                    diff_to_token_based = abs(sell_amount_out - sell_amount_out)  # 这个总是0

                    if diff_to_buy_based < 0.01:  # 如果非常接近基于buy_amount_out的计算
                        logger.error(f"🚨 严重错误：sell_amount_out似乎是基于buy_amount_out计算的！")
                        logger.error(f"     这违反了我们的设计，卖出应该基于token_amount！")

            # 检查卖出结果是否合理
            if token_amount > 0 and sell_amount_out > 0:
                price_per_token = sell_amount_out / token_amount
                logger.info(f"卖出价格检查: {sell_amount_out} USDT / {token_amount} {symbol} = {price_per_token:.6f} USDT per token")

                # 如果buy_amount_out和token_amount差异很大，记录警告
                if abs(buy_amount_out - token_amount) > token_amount * 0.1:  # 差异超过10%
                    logger.warning(f"⚠️  买入输出与原始token_amount差异较大:")
                    logger.warning(f"   buy_amount_out: {buy_amount_out}")
                    logger.warning(f"   token_amount: {token_amount}")
                    logger.warning(f"   差异: {abs(buy_amount_out - token_amount):.6f} ({abs(buy_amount_out - token_amount)/token_amount*100:.2f}%)")

                    # 检查sell_amount_out是否更接近基于buy_amount_out的预期值
                    expected_sell_from_buy = buy_amount_out * price_per_token
                    logger.warning(f"   如果基于buy_amount_out计算: {buy_amount_out} * {price_per_token:.6f} = {expected_sell_from_buy:.6f}")
                    logger.warning(f"   实际sell_amount_out: {sell_amount_out}")

                    if abs(sell_amount_out - expected_sell_from_buy) < abs(sell_amount_out - (token_amount * price_per_token)):
                        logger.error(f"🚨 检测到问题：sell_amount_out更接近基于buy_amount_out的计算结果！")
                        logger.error(f"   这说明卖出模拟可能使用了buy_amount_out而不是token_amount！")

            # 计算总利润（不考虑桥费用）
            total_gas_cost = buy_gas_cost + sell_gas_cost
            gross_profit = sell_amount_out - usdt_amount
            net_profit = gross_profit - total_gas_cost

            logger.info(f"总Gas成本: ${total_gas_cost}")
            logger.info(f"毛利润: ${gross_profit}")
            logger.info(f"净利润: ${net_profit}")

            # 简化验证：当净利润超过2时，直接对比usdt_output和sell_amount_out
            if verification_success and net_profit > 2:
                logger.warning(f"{symbol} 检测到异常高利润 {net_profit:.2f} USDT，进行简化验证...")

                # 从token_info中获取原始分析的expected_usdt_out
                original_usdt_output = token_info.get('expected_usdt_out', 0)
                if original_usdt_output == 0:
                    logger.error(f"{symbol} 无法获取原始expected_usdt_out，跳过验证")
                    verification_success = False
                    reason = "高利润验证失败：无法获取原始预期输出"
                else:
                    # 直接对比原始分析的usdt_output和模拟交易的sell_amount_out
                    output_difference = abs(original_usdt_output - sell_amount_out)

                    logger.info(f"{symbol} 简化验证结果:")
                    logger.info(f"  使用token_amount: {token_amount} {symbol}")
                    logger.info(f"  原始分析expected_usdt_out: {original_usdt_output} USDT")
                    logger.info(f"  模拟交易sell_amount_out: {sell_amount_out} USDT")
                    logger.info(f"  输出差异: {output_difference} USDT")

                    # 验证成功条件：差异低于2
                    if output_difference >= 2:
                        logger.error(f"{symbol} 验证失败：输出差异 {output_difference:.2f} USDT >= 2，可能是原始分析数据错误")
                        verification_success = False
                        reason = f"高利润验证失败：输出差异过大 ({output_difference:.2f} USDT)"

                        # 保存验证失败的机会信息
                        failed_info = {
                            "symbol": symbol,
                            "usdt_input": usdt_amount,
                            "token_amount": token_amount,
                            "original_usdt_output": original_usdt_output,
                            "sell_amount_out": sell_amount_out,
                            "output_difference": output_difference,
                            "net_profit": net_profit,
                            "bridge_direction": bridge_direction,
                            "failure_reason": reason
                        }

                        # 保存失败信息
                        try:
                            save_failed_validation_opportunities(failed_info)
                        except Exception as save_error:
                            logger.error(f"保存失败验证信息时出错: {str(save_error)}")

                    else:
                        logger.info(f"{symbol} 验证通过：输出差异 {output_difference:.2f} USDT < 2")
                        reason = "模拟交易验证成功（包含高利润简化验证）"

        
            # 构建返回结果
            result = {
                "verification_success": verification_success,
                "reason": reason,
                "symbol": symbol,
                "buy_success": buy_success,
                "sell_success": sell_success,
                "buy_reason": buy_reason if not buy_success else "",
                "sell_reason": sell_reason if not sell_success else "",
                "bridge_direction": bridge_direction
            }
            
            # 如果都成功，添加更多详细信息
            if verification_success:
                result.update({
                    "buy_amount_out": buy_amount_out,
                    "sell_amount_out": sell_amount_out,
                    "buy_gas_cost": buy_gas_cost,
                    "sell_gas_cost": sell_gas_cost,
                    "total_gas_cost": total_gas_cost,
                    "gross_profit": gross_profit,
                    "net_profit": net_profit
                })
            
            return result
        
        except Exception as e:
            logger.error(f"验证 {symbol} 的套利路径时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return {
                "verification_success": False,
                "reason": str(e),
                "symbol": symbol
            }

    async def analyze_token(self, token_info: Dict) -> Dict:
        """
        分析单个代币，同时进行验证和分析
        
        Args:
            token_info: 代币信息
            
        Returns:
            Dict: 分析结果
        """
        symbol = token_info['symbol']
        polygon_address = token_info['polygon_address']
        eth_address = token_info['eth_address']
        optimal_usdt = token_info['optimal_usdt']
        token_data = token_info['token_data']
        
        # 确保bridge_direction正确获取，优先从token_info获取，然后从token_data获取
        bridge_direction = token_info.get('bridge_direction', '')
        if not bridge_direction and token_data and 'bridge_direction' in token_data:
            bridge_direction = token_data.get('bridge_direction', '')
        
        # 记录详细的桥方向信息
        logger.info(f"接收到的桥方向: {bridge_direction}")
        
        if bridge_direction:
            logger.info(f"开始对 {symbol} 进行二次分析，最优USDT输入值: {optimal_usdt}，交易方向: {bridge_direction}")
        else:
            # 如果还是没有bridge_direction，尝试从价格差异确定
            eth_price = token_info.get('eth_price', 0)
            polygon_price = token_info.get('polygon_price', 0)
            
            if eth_price > 0 and polygon_price > 0:
                if eth_price > polygon_price:
                    bridge_direction = 'polygon_to_ethereum'
                    logger.info(f"{symbol}: 以太坊价格({eth_price})>Polygon价格({polygon_price})，方向为polygon_to_ethereum")
                else:
                    bridge_direction = 'ethereum_to_polygon'
                    logger.info(f"{symbol}: Polygon价格({polygon_price})>=以太坊价格({eth_price})，方向为ethereum_to_polygon")
            else:
                # 如果还是没有足够信息，尝试从lower_chain/higher_chain确定
                lower_chain = token_info.get('lower_chain', '')
                if lower_chain:
                    bridge_direction = "polygon_to_ethereum" if lower_chain == "polygon" else "ethereum_to_polygon"
                    logger.info(f"{symbol}: 从lower_chain({lower_chain})确定方向为{bridge_direction}")
                else:
                    logger.warning(f"{symbol}: 无法确定交易方向，将影响验证过程")
        
        # 无论如何，确保bridge_direction被设置
        if not bridge_direction:
            # 如果实在无法确定，默认使用ethereum_to_polygon
            bridge_direction = 'ethereum_to_polygon'
            logger.warning(f"{symbol}: 无法确定交易方向，默认使用{bridge_direction}")
        
        # 确保token_data中也包含bridge_direction
        if token_data:
            token_data['bridge_direction'] = bridge_direction
        
        # 获取token_amount，从初次分析中获取的代币数量
        token_amount = token_info.get('token_amount')
        if token_amount is None or token_amount <= 0:
            logger.error(f"{symbol}: 缺少必要的代币数量信息，无法进行模拟交易验证")
            return {
                "success": False,
                "error": "缺少必要的代币数量信息",
                "symbol": symbol
            }
        
        logger.info(f"{symbol}: 使用代币数量 {token_amount} 进行模拟交易")
        
        # 创建验证对象，确保包含正确的交易方向和代币数量
        verification_opportunity = {
            'symbol': symbol,
            'polygon_address': polygon_address,
            'eth_address': eth_address,
            'bridge_direction': bridge_direction,
            'optimal_usdt': optimal_usdt,
            'token_amount': token_amount  # 使用正确的代币数量
        }
        
        # 生成测试金额
        test_amounts = self.generate_test_amounts(optimal_usdt)
        logger.info(f"为 {symbol} 生成了 {len(test_amounts)} 个测试金额: {test_amounts}")
        
        # 同时启动所有三个验证任务 - 使用线程池确保真正的并发执行
        logger.info(f"同时启动 {symbol} 的模拟交易验证、地址验证和利润分析任务")
        
        # 导入线程池
        import concurrent.futures
        
        # 定义在独立线程中运行协程的函数
        def run_coro_in_thread(coro_func, *args, **kwargs):
            # 创建一个新的事件循环
            new_loop = asyncio.new_event_loop()
            asyncio.set_event_loop(new_loop)
            try:
                # 运行协程并返回结果
                return new_loop.run_until_complete(coro_func(*args, **kwargs))
            finally:
                # 关闭事件循环
                new_loop.close()
        
        # 使用线程池完全并行执行三个任务
        simulation_result = None
        verification_result = None
        analysis_result = None
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            # 提交三个完全独立的任务
            simulation_future = executor.submit(
                run_coro_in_thread, 
                self.verify_arbitrage_path, 
                verification_opportunity
            )
            
            verification_future = executor.submit(
                run_coro_in_thread,
                self._verify_addresses_async,
                verification_opportunity
            )
            
            analysis_future = executor.submit(
                run_coro_in_thread,
                self._analyze_amounts,
                symbol, polygon_address, eth_address, token_data, test_amounts, optimal_usdt
            )
            
            # 获取结果 - 不会相互阻塞
            try:
                simulation_result = simulation_future.result(timeout=120)
                logger.info(f"{symbol} 模拟交易验证已完成: {'通过' if simulation_result.get('verification_success', False) else '未通过'}")
            except concurrent.futures.TimeoutError:
                logger.error(f"{symbol} 模拟交易验证超时")
                simulation_result = {
                    "verification_success": False,
                    "reason": "模拟验证超时",
                    "symbol": symbol
                }
            except Exception as e:
                logger.error(f"{symbol} 模拟交易验证出错: {str(e)}")
                simulation_result = {
                    "verification_success": False,
                    "reason": f"验证出错: {str(e)}",
                    "symbol": symbol
                }
            
            try:
                verification_result = verification_future.result(timeout=120)
                logger.info(f"{symbol} 地址验证已完成: {'通过' if verification_result.get('address_verified', False) else '未通过'}")
            except concurrent.futures.TimeoutError:
                logger.error(f"{symbol} 地址验证超时")
                verification_result = {
                    "address_verified": False,
                    "reason": "地址验证超时",
                    "symbol": symbol
                }
            except Exception as e:
                logger.error(f"{symbol} 地址验证出错: {str(e)}")
                verification_result = {
                    "address_verified": False,
                    "reason": f"验证出错: {str(e)}",
                    "symbol": symbol
                }
            
            try:
                analysis_result = analysis_future.result(timeout=120)
                opps_count = len(analysis_result.get("opportunities", []))
                logger.info(f"{symbol} 利润分析已完成: {'找到 ' + str(opps_count) + ' 个可能的机会' if opps_count > 0 else '未找到任何机会'}")
            except concurrent.futures.TimeoutError:
                logger.error(f"{symbol} 利润分析超时")
                analysis_result = {
                    "success": False,
                    "error": "利润分析超时",
                    "opportunities": []
                }
            except Exception as e:
                logger.error(f"{symbol} 利润分析出错: {str(e)}")
                analysis_result = {
                    "success": False,
                    "error": str(e),
                    "opportunities": []
                }
        
        # 获取验证结果
        address_verified = verification_result.get('address_verified', False)
        verification_reason = verification_result.get('reason', '')
        
        # 获取模拟交易结果
        simulation_verified = simulation_result.get('verification_success', False)
        simulation_reason = simulation_result.get('reason', '')
        
        # 获取分析结果
        opportunities = analysis_result.get("opportunities", [])
        
        # 如果模拟交易失败，检查失败原因
        if not simulation_verified:
            # 检查是否是高利润验证失败
            if "高利润验证失败" in simulation_reason or "输出差异过大" in simulation_reason:
                logger.error(f"{symbol} 高利润验证失败: {simulation_reason}，跳过此机会")
                return None  # 直接返回None，不保存此机会
            else:
                logger.warning(f"{symbol} 模拟交易验证失败: {simulation_reason}，但仍会处理地址验证和利润分析结果")
        
        # 找出最优机会
        if opportunities:
            # 按净利润排序
            sorted_opps = sorted(opportunities, key=lambda x: x["net_profit"], reverse=True)
            best_opportunity = sorted_opps[0]
            
            # 添加额外的分析字段
            best_opportunity['profit_above_1usdt'] = best_opportunity['net_profit'] > 1.0
            best_opportunity['profit_above_half_gas'] = best_opportunity['net_profit'] > (0.5 * best_opportunity['total_gas_cost'])
            best_opportunity['analysis_time'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            best_opportunity['is_secondary_analysis'] = True
            
            # 添加验证结果和交易方向
            best_opportunity['address_verified'] = address_verified
            best_opportunity['verification_reason'] = verification_reason
            best_opportunity['bridge_direction'] = bridge_direction
            
            # 添加模拟交易结果
            best_opportunity['simulation_verified'] = simulation_verified
            best_opportunity['simulation_reason'] = simulation_reason
            
            # 添加模拟交易的详细数据
            if simulation_verified and 'buy_amount_out' in simulation_result:
                best_opportunity['buy_amount_out'] = simulation_result.get('buy_amount_out', 0)
                best_opportunity['sell_amount_out'] = simulation_result.get('sell_amount_out', 0)
                best_opportunity['buy_gas_cost'] = simulation_result.get('buy_gas_cost', 0)
                best_opportunity['sell_gas_cost'] = simulation_result.get('sell_gas_cost', 0)
                best_opportunity['simulation_gas_cost'] = simulation_result.get('total_gas_cost', 0)
            
            # 提供详细的分析结果日志
            logger.info(f"找到 {symbol} 的最优套利机会:")
            logger.info(f"  投入金额: {best_opportunity['usdt_input']:.2f} USDT")
            logger.info(f"  获得金额: {best_opportunity['usdt_output']:.2f} USDT")
            logger.info(f"  毛利润: {best_opportunity['gross_profit']:.2f} USDT ({best_opportunity['profit_percentage']:.2f}%)")
            logger.info(f"  总Gas成本: {best_opportunity['total_gas_cost']:.2f} USDT")
            logger.info(f"  净利润: {best_opportunity['net_profit']:.2f} USDT ({best_opportunity['net_profit_percentage']:.2f}%)")
            logger.info(f"  利润大于1USDT: {'是' if best_opportunity['profit_above_1usdt'] else '否'}")
            logger.info(f"  利润大于0.5倍Gas成本: {'是' if best_opportunity['profit_above_half_gas'] else '否'}")
            logger.info(f"  地址验证通过: {'是' if address_verified else '否'}")
            logger.info(f"  模拟交易验证通过: {'是' if simulation_verified else '否'}")
            logger.info(f"  交易方向: {bridge_direction}")
            
            # 检查是否满足所有条件
            if (best_opportunity['profit_above_1usdt'] and 
                best_opportunity['profit_above_half_gas'] and 
                address_verified and 
                simulation_verified):
                
                logger.info(f"{symbol} 满足所有条件，保存为已验证机会")
                
                # 保存到已验证机会
                with self.lock:
                    self.verified_opportunities.append(best_opportunity)
                    save_verified_opportunities([best_opportunity])
            else:
                logger.warning(f"{symbol} 不满足全部条件，跳过保存到已验证机会")
                if not best_opportunity['profit_above_1usdt']:
                    logger.warning(f"  原因: 利润不足1USDT")
                if not best_opportunity['profit_above_half_gas']:
                    logger.warning(f"  原因: 利润不足0.5倍Gas成本")
                if not address_verified:
                    logger.warning(f"  原因: 地址验证未通过 - {verification_reason}")
                if not simulation_verified:
                    logger.warning(f"  原因: 模拟交易验证未通过 - {simulation_reason}")
            
            # 保存分析结果
            with self.lock:
                self.opportunities.append(best_opportunity)
                self.save_opportunities()
            
            # 列出所有找到的机会，按利润排序
            if len(sorted_opps) > 1:
                logger.info(f"所有可能的套利机会 (按净利润排序):")
                for i, opp in enumerate(sorted_opps[:5], 1):  # 只显示前5个
                    logger.info(f"  {i}. 投入: {opp['usdt_input']:.2f} USDT, 净利润: {opp['net_profit']:.2f} USDT ({opp['net_profit_percentage']:.2f}%)")
                if len(sorted_opps) > 5:
                    logger.info(f"  ... 还有 {len(sorted_opps) - 5} 个可能的机会")
        else:
            logger.warning(f"{symbol} 二次分析未找到任何套利机会")
        
        logger.info(f"{symbol} 的二次分析已完成")
        
        return {
            "success": True,
            "symbol": symbol,
            "opportunities": opportunities,
            "address_verified": address_verified,
            "verification_reason": verification_reason,
            "simulation_verified": simulation_verified,
            "simulation_reason": simulation_reason,
            "bridge_direction": bridge_direction
        }

    # 添加一个辅助方法处理多个金额的分析
    async def _analyze_amounts(self, symbol, polygon_address, eth_address, token_data, test_amounts, optimal_usdt):
        """分析多个测试金额"""
        opportunities = []
        failed_count = 0
        
        # 使用真正的多线程处理每个测试金额
        with concurrent.futures.ThreadPoolExecutor(max_workers=len(test_amounts)) as executor:
            # 为每个测试金额创建一个任务
            future_to_amount = {}
            for amount in test_amounts:
                future = executor.submit(
                    self.process_amount_in_thread, 
                    polygon_address, 
                    eth_address, 
                    token_data, 
                    amount
                )
                future_to_amount[future] = amount
            
            # 处理结果
            for future in concurrent.futures.as_completed(future_to_amount):
                amount = future_to_amount[future]
                try:
                    result = future.result()
                    if result.get("success") and "opportunities" in result and result["opportunities"]:
                        logger.info(f"{symbol} 使用金额 {amount} USDT 分析成功")
                        opportunities.extend(result["opportunities"])
                    else:
                        logger.warning(f"{symbol} 使用金额 {amount} USDT 分析未找到机会")
                except Exception as e:
                    failed_count += 1
                    logger.error(f"{symbol} 使用金额 {amount} USDT 分析失败: {str(e)}")
        
        # 如果失败次数过多，使用5等分重试
        if failed_count > 5 and failed_count == len(test_amounts):
            logger.warning(f"{symbol} 所有分析都失败，使用5等分重试")
            
            # 生成5个等分点
            amounts = np.linspace(max(self.arb_finder.min_usdt_amount, optimal_usdt * 0.5), 
                                  min(self.arb_finder.max_usdt_amount, optimal_usdt * 2.0), 5)
            test_amounts = [round(amount, 2) for amount in amounts]
            logger.info(f"为 {symbol} 生成了 5 个重试金额: {test_amounts}")
            
            # 使用更少的线程重试
            with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
                # 为每个测试金额创建一个任务
                future_to_amount = {}
                for amount in test_amounts:
                    future = executor.submit(
                        self.process_amount_in_thread, 
                        polygon_address, 
                        eth_address, 
                        token_data, 
                        amount
                    )
                    future_to_amount[future] = amount
                
                # 处理结果
                for future in concurrent.futures.as_completed(future_to_amount):
                    amount = future_to_amount[future]
                    try:
                        result = future.result()
                        if result.get("success") and "opportunities" in result and result["opportunities"]:
                            logger.info(f"{symbol} 重试使用金额 {amount} USDT 分析成功")
                            opportunities.extend(result["opportunities"])
                        else:
                            logger.warning(f"{symbol} 重试使用金额 {amount} USDT 分析未找到机会")
                    except Exception as e:
                        logger.error(f"{symbol} 重试使用金额 {amount} USDT 分析失败: {str(e)}")
        
        return {
            "success": True,
            "opportunities": opportunities
        }
    
    def process_amount_in_thread(self, polygon_address: str, eth_address: str, token_data: Dict, amount: float) -> Dict:
        """
        在独立线程中处理单个测试金额
        
        Args:
            polygon_address: Polygon链上的代币地址
            eth_address: 以太坊链上的代币地址
            token_data: 代币数据
            amount: USDT测试金额
            
        Returns:
            Dict: 分析结果
        """
        symbol = token_data.get("symbol", "UNKNOWN")
        try:
            # 创建一个新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # 禁用其他模块的日志记录，避免重复日志
            for log_name in ['urllib3', 'web3', 'asyncio']:
                logging.getLogger(log_name).setLevel(logging.WARNING)
            
            # 创建一个独立的BridgeArbFinder实例，避免共享状态
            arb_finder = BridgeArbFinder(num_workers=1)  # 只需要1个工作线程
            
            # 禁用arb_finder的日志输出功能，避免重复日志
            arb_finder.collect_log = lambda *args, **kwargs: None
            arb_finder.print_token_logs = lambda *args, **kwargs: None
            
            # 运行分析，直接传递固定的USDT金额
            result = loop.run_until_complete(
                arb_finder.check_arbitrage_opportunity(
                    (polygon_address, eth_address),
                    token_data,
                    check_recent=False,  # 二次分析时不检查最近的机会
                    fixed_usdt_amount=amount  # 直接传递固定的USDT金额
                )
            )
            
            # 关闭事件循环
            loop.close()
            
            return result
        except Exception as e:
            logger.error(f"处理 {symbol} 金额 {amount} USDT 时出错: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def save_opportunities(self) -> None:
        """保存每个代币的最优套利机会到文件"""
        # 定义文件路径
        json_file = os.path.join(RESULTS_DIR, "secondary_opportunities.json")
        csv_file = os.path.join(RESULTS_DIR, "secondary_opportunities.csv")
        
        if not self.opportunities:
            return
        
        try:
            # 按代币分组，只保留每个代币的最优机会
            best_opportunities = {}
            for opp in self.opportunities:
                symbol = opp['symbol']
                if symbol not in best_opportunities or opp['net_profit'] > best_opportunities[symbol]['net_profit']:
                    best_opportunities[symbol] = opp
            
            # 转换为列表
            new_opps = list(best_opportunities.values())
            
            # 读取现有文件数据
            all_opps = []
            if os.path.exists(json_file):
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        all_opps = json.load(f)
                    logger.info(f"读取到 {len(all_opps)} 条现有二次分析记录")
                except Exception as e:
                    logger.warning(f"读取现有JSON文件失败: {str(e)}")
            
            # 添加新的分析字段：利润大于1USDT和利润大于0.5倍总gas消耗
            for opp in new_opps:
                # 确保使用Python原生布尔值
                opp['profit_above_1usdt'] = bool(opp['net_profit'] > 1.0)
                opp['profit_above_half_gas'] = bool(opp['net_profit'] > (0.5 * opp['total_gas_cost']))
                # 添加时间戳，确保每次分析的记录都有唯一标识
                opp['analysis_time'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                # 添加唯一标识，确保记录是唯一的
                opp['analysis_id'] = f"{opp['symbol']}_{opp['usdt_input']}_{opp['analysis_time']}"
            
            # 直接追加新记录，不替换已有记录
            all_opps.extend(new_opps)
            logger.info(f"追加 {len(new_opps)} 条新记录，总计 {len(all_opps)} 条记录")
            
            # 处理NumPy类型
            all_opps = convert_numpy_types(all_opps)
            
            # 保存JSON
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(all_opps, f, indent=2, ensure_ascii=False)
                
            # 生成CSV报告
            df = pd.DataFrame(all_opps)
            df.to_csv(csv_file, index=False)
            
            logger.info(f"已保存 {len(all_opps)} 条二次分析结果到文件")
        except Exception as e:
            logger.error(f"保存二次分析结果时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())

    async def run_analysis(self):
        """运行二次分析"""
        # 获取需要分析的代币
        tokens_to_analyze = await self.get_profitable_tokens()
        
        if not tokens_to_analyze:
            logger.info("没有找到需要二次分析的代币")
            return
        
        logger.info(f"找到 {len(tokens_to_analyze)} 个需要二次分析的代币")
        
        # 创建任务列表
        tasks = []
        for token_info in tokens_to_analyze:
            # 确保token_info包含所有必要的字段
            symbol = token_info.get('symbol', 'Unknown')
            
            # 检查必要字段
            required_fields = ['symbol', 'polygon_address', 'eth_address', 'bridge_direction', 'token_amount']
            missing_fields = [field for field in required_fields if field not in token_info or not token_info[field]]
            
            if missing_fields:
                logger.error(f"{symbol}: 缺少必要字段 {missing_fields}，跳过分析")
                continue
                
            # 详细记录代币信息
            logger.info(f"准备分析代币: {symbol}")
            logger.info(f"  Polygon地址: {token_info['polygon_address']}")
            logger.info(f"  以太坊地址: {token_info['eth_address']}")
            logger.info(f"  桥方向: {token_info['bridge_direction']}")
            logger.info(f"  USDT输入量: {token_info.get('optimal_usdt', 'N/A')}")
            logger.info(f"  代币数量: {token_info['token_amount']}")
            
            # 添加分析任务
            tasks.append(self.analyze_token(token_info))
        
        # 并行执行任务
        results = await asyncio.gather(*tasks)

        # 过滤掉None结果（高利润验证失败的情况）
        valid_results = [r for r in results if r is not None]
        filtered_count = len(results) - len(valid_results)

        if filtered_count > 0:
            logger.info(f"过滤掉 {filtered_count} 个高利润验证失败的机会")

        # 处理结果
        success_count = sum(1 for r in valid_results if r.get("success"))
        logger.info(f"二次分析完成，成功分析 {success_count}/{len(valid_results)} 个代币（已过滤 {filtered_count} 个无效机会）")
        
        # 等待所有验证线程完成
        logger.info("等待所有验证线程完成...")
        time.sleep(5)  # 给验证线程一些时间完成
        
        # 汇总验证结果
        with self.lock:
            verified_count = len(self.verified_opportunities)
        
        if verified_count > 0:
            logger.info(f"共有 {verified_count} 个套利机会通过了地址和利润验证")
        else:
            logger.info("没有套利机会通过地址和利润验证")

def save_failed_validation_opportunities(failed_opportunity: Dict) -> None:
    """
    保存额外验证失败的机会，只保存最新的20条记录

    Args:
        failed_opportunity: 验证失败的机会信息
    """
    try:
        # 确保目录存在
        results_dir = os.path.join(os.path.dirname(__file__), "results")
        os.makedirs(results_dir, exist_ok=True)

        failed_file = os.path.join(results_dir, "failed_validation_opportunities.json")

        # 读取现有数据
        failed_opportunities = []
        if os.path.exists(failed_file):
            try:
                with open(failed_file, 'r', encoding='utf-8') as f:
                    failed_opportunities = json.load(f)
                if not isinstance(failed_opportunities, list):
                    failed_opportunities = []
            except Exception as e:
                logger.error(f"读取失败验证文件出错: {str(e)}")
                failed_opportunities = []

        # 添加时间戳和失败原因
        failed_opportunity['failed_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        failed_opportunity['failure_type'] = 'high_profit_validation_failed'

        # 添加新记录到列表开头
        failed_opportunities.insert(0, failed_opportunity)

        # 只保留最新的20条记录
        if len(failed_opportunities) > 20:
            failed_opportunities = failed_opportunities[:20]

        # 保存更新后的数据
        with open(failed_file, 'w', encoding='utf-8') as f:
            json.dump(failed_opportunities, f, indent=2, ensure_ascii=False)

        logger.info(f"已保存验证失败的机会到 {failed_file}，当前共 {len(failed_opportunities)} 条记录")

    except Exception as e:
        logger.error(f"保存验证失败机会时出错: {str(e)}")
        logger.error(traceback.format_exc())

def save_verified_opportunities(opportunities: List[Dict]) -> None:
    """保存已验证的套利机会，并自动执行交易"""
    try:
        if not opportunities:
            logger.info("没有新的验证过的套利机会需要保存")
            return
            
        # 确保目录存在
        os.makedirs(os.path.dirname(VERIFIED_OPPS_FILE), exist_ok=True)
        
        # 设置固定的结果目录
        PROJECT_ROOT = r"C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev"
        RESULTS_DIR = os.path.join(PROJECT_ROOT, "scripts", "arbitrage", "portal_polygon_bridge", "results")
        os.makedirs(RESULTS_DIR, exist_ok=True)
        
        # 将numpy类型转换为Python原生类型
        opportunities = convert_numpy_types(opportunities)
        
        # 读取现有的验证机会
        existing_opps = []
        
        if os.path.exists(VERIFIED_OPPS_FILE):
            try:
                with open(VERIFIED_OPPS_FILE, 'r', encoding='utf-8') as f:
                    existing_opps = json.load(f)
                    if not isinstance(existing_opps, list):
                        existing_opps = [existing_opps] if existing_opps else []
                logger.info(f"读取到 {len(existing_opps)} 条现有验证记录")
            except Exception as e:
                logger.error(f"读取现有验证机会时出错: {str(e)}")
                existing_opps = []
                
        # 添加时间戳和唯一ID
        new_opps = []
        for opp in opportunities:
            opp = convert_numpy_types(opp)  # 确保嵌套字典也被转换
            opp['verification_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            # 创建唯一ID
            if 'hash' not in opp:
                opp['hash'] = f"{opp.get('symbol')}_{opp.get('usdt_input')}_{opp.get('verification_time')}"
            new_opps.append(opp)
                
        # 检查现有机会，防止重复
        filtered_opps = []
        for new_opp in new_opps:
            is_duplicate = False
            for existing_opp in existing_opps:
                # 如果哈希值相同，则视为重复
                if 'hash' in new_opp and 'hash' in existing_opp and new_opp['hash'] == existing_opp['hash']:
                    is_duplicate = True
                    break
                    
                # 如果符号、链和时间戳相近，也视为重复
                if (new_opp.get('symbol') == existing_opp.get('symbol') and
                    new_opp.get('lower_chain') == existing_opp.get('lower_chain')):
                    # 检查时间戳是否在5分钟内
                    try:
                        new_time = datetime.strptime(new_opp.get('verification_time', ''), '%Y-%m-%d %H:%M:%S')
                        existing_time = datetime.strptime(existing_opp.get('verification_time', ''), '%Y-%m-%d %H:%M:%S')
                        if (new_time - existing_time).total_seconds() < 300:  # 5分钟 = 300秒
                            is_duplicate = True
                            break
                    except:
                        # 如果时间解析失败，继续检查
                        pass
                        
            if not is_duplicate:
                filtered_opps.append(new_opp)
                
        # 如果所有机会都是重复的，则退出
        if not filtered_opps:
            logger.info("所有机会都是重复的，不保存新记录")
            return
                
        # 合并新的和现有的
        combined_opps = existing_opps + filtered_opps
        
        # 如果文件太大，只保留最新的100条记录
        if len(combined_opps) > 100:
            # 按时间戳排序，保留最新的100条
            combined_opps.sort(key=lambda x: x.get('verification_time', ''), reverse=True)
            combined_opps = combined_opps[:100]
            logger.info(f"已修剪验证记录，仅保留最新的100条记录")
        
        # 保存回文件
        with open(VERIFIED_OPPS_FILE, 'w', encoding='utf-8') as f:
            json.dump(combined_opps, f, indent=2, ensure_ascii=False)
            
        logger.info(f"追加 {len(filtered_opps)} 条验证记录，总计 {len(combined_opps)} 条记录")
        logger.info(f"已保存 {len(combined_opps)} 条验证结果到文件")
            
        # 直接启用自动执行交易，无需环境变量检查
        if filtered_opps:
            logger.info(f"自动执行交易已启用，将在新线程中执行 {len(filtered_opps)} 个验证过的交易")
            
            # 1. 首先尝试导入优化的execute_trade_in_thread函数
            try:
                logger.info("尝试导入bridge_arb_executor中的execute_trade_in_thread函数")
                from scripts.arbitrage.portal_polygon_bridge.bridge_arb_executor import execute_trade_in_thread
                
                executor_found = True
                logger.info("成功导入execute_trade_in_thread函数")
                
                # 执行每个交易机会
                for opp in filtered_opps:
                    try:
                        symbol = opp.get('symbol')
                        usdt_input = opp.get('usdt_input')
                        
                        # 获取交易日志文件名称（路径由executor自行生成）
                        trade_log_file = f"trade_{symbol}.log"
                        
                        # 确保机会对象包含所有必要信息
                        if not opp.get('lower_chain'):
                            # 尝试从bridge_direction推断lower_chain
                            bridge_direction = opp.get('bridge_direction', '')
                            if bridge_direction == 'ethereum_to_polygon':
                                opp['lower_chain'] = 'ethereum'
                            elif bridge_direction == 'polygon_to_ethereum':
                                opp['lower_chain'] = 'polygon'
                            else:
                                logger.warning(f"{symbol}: 未知的交易方向 {bridge_direction}，无法确定lower_chain")
                                continue
                        
                        # 获取正确的chain值和token地址
                        chain = opp.get('lower_chain')
                        
                        # 确保eth_address和polygon_address都存在
                        if not opp.get('eth_address') or not opp.get('polygon_address'):
                            logger.warning(f"{symbol}: 缺少以太坊或Polygon地址，跳过执行")
                            continue
                        
                        # 确保链地址严格匹配
                        if chain == 'ethereum':
                            token_address = opp.get('eth_address')
                        else:  # polygon
                            token_address = opp.get('polygon_address')
                        
                        logger.info("=" * 60)
                        logger.info(f"准备在新线程中执行 {symbol} 买入交易")
                        logger.info(f"交易详情:")
                        logger.info(f"  链: {chain}")
                        logger.info(f"  使用地址: {token_address}")
                        logger.info(f"  以太坊地址: {opp.get('eth_address')}")
                        logger.info(f"  Polygon地址: {opp.get('polygon_address')}")
                        logger.info(f"  USDT投入: {usdt_input} USDT")
                        logger.info(f"  交易方向: {opp.get('bridge_direction', 'Unknown')}")
                        logger.info(f"  日志文件: {trade_log_file}")
                        
                        # 确保opportunity包含所有必要字段
                        if 'hash' not in opp:
                            opp['hash'] = f"{symbol}_{chain}_{usdt_input}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
                        
                        # 记录交易时间
                        opp['execution_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        
                        # 在新线程中执行交易
                        logger.info(f"正在将交易任务传递给execute_trade_in_thread")
                        
                        # 执行交易任务 - bridge_arb_executor会自己保存结果
                        execute_trade_in_thread(opp)
                        logger.info(f"{symbol} 交易任务已在新线程中启动")
                        
                        # 添加一个延迟，避免过快启动多个线程
                        time.sleep(2)
                        
                    except Exception as e:
                        logger.error(f"启动 {opp.get('symbol')} 交易线程时出错: {str(e)}")
                        logger.error(traceback.format_exc())
                        
                        # 如果主方法失败，尝试使用备用方法
                        logger.warning(f"尝试使用备用方法执行 {opp.get('symbol')} 交易")
                        # 使用subprocess方式启动执行器
                        try:
                            symbol = opp.get('symbol', 'Unknown')
                            usdt_input = opp.get('usdt_input', 0)
                            
                            # 确定交易方向和链
                            bridge_direction = opp.get('bridge_direction', '')
                            
                            # 根据bridge_direction确定lower_chain
                            if bridge_direction == 'ethereum_to_polygon':
                                chain = 'ethereum'
                            elif bridge_direction == 'polygon_to_ethereum':
                                chain = 'polygon'
                            else:
                                # 直接使用lower_chain
                                chain = opp.get('lower_chain', '')
                                if not chain:
                                    logger.error(f"{symbol}: 未知的交易方向 {bridge_direction}，无法确定交易链")
                                    continue
                            
                            # 获取正确的token_address
                            if chain == 'ethereum':
                                token_address = opp.get('eth_address', '')
                            else:
                                token_address = opp.get('polygon_address', '')
                            
                            if not token_address:
                                logger.error(f"{symbol}: 在{chain}链上没有找到代币地址")
                                continue
                            
                            logger.info(f"使用备用方法执行 {symbol} 买入交易，投入: {usdt_input} USDT，链: {chain}，代币地址: {token_address}")
                            
                            # 构建命令
                            executor_script = os.path.join("scripts", "arbitrage", "portal_polygon_bridge", "bridge_arb_executor.py")
                            cmd = [
                                "python", 
                                executor_script,
                                "--symbol", symbol,
                                "--chain", chain,
                                "--amount", str(usdt_input),
                                "--token-address", token_address
                            ]
                            
                            # 启动进程
                            logger.info(f"启动新进程执行交易: {' '.join(cmd)}")
                            
                            # 使用subprocess.Popen启动进程
                            import subprocess
                            process = subprocess.Popen(cmd)
                            
                            logger.info(f"{symbol} 交易已提交给执行器进程，PID: {process.pid}")
                        except Exception as e:
                            logger.error(f"使用备用方法执行 {opp.get('symbol', 'Unknown')} 交易时出错: {str(e)}")
                            logger.error(traceback.format_exc())
                
                logger.info(f"所有交易任务已启动，总计 {len(filtered_opps)} 个")
                
            except ImportError as e:
                logger.error(f"导入交易执行模块时出错: {str(e)}")
                logger.error(traceback.format_exc())
                
                # 使用备用方法执行交易
                logger.warning("使用备用方法执行交易")
                for opp in filtered_opps:
                    try:
                        symbol = opp.get('symbol', 'Unknown')
                        usdt_input = opp.get('usdt_input', 0)
                        
                        # 确定交易方向和链
                        bridge_direction = opp.get('bridge_direction', '')
                        
                        # 根据bridge_direction确定lower_chain
                        if bridge_direction == 'ethereum_to_polygon':
                            chain = 'ethereum'
                        elif bridge_direction == 'polygon_to_ethereum':
                            chain = 'polygon'
                        else:
                            # 直接使用lower_chain
                            chain = opp.get('lower_chain', '')
                            if not chain:
                                logger.error(f"{symbol}: 未知的交易方向 {bridge_direction}，无法确定交易链")
                                continue
                        
                        # 获取正确的token_address
                        if chain == 'ethereum':
                            token_address = opp.get('eth_address', '')
                        else:
                            token_address = opp.get('polygon_address', '')
                        
                        if not token_address:
                            logger.error(f"{symbol}: 在{chain}链上没有找到代币地址")
                            continue
                        
                        logger.info(f"使用备用方法执行 {symbol} 买入交易，投入: {usdt_input} USDT，链: {chain}，代币地址: {token_address}")
                        
                        # 构建命令
                        executor_script = os.path.join("scripts", "arbitrage", "portal_polygon_bridge", "bridge_arb_executor.py")
                        cmd = [
                            "python", 
                            executor_script,
                            "--symbol", symbol,
                            "--chain", chain,
                            "--amount", str(usdt_input),
                            "--token-address", token_address
                        ]
                        
                        # 启动进程
                        logger.info(f"启动新进程执行交易: {' '.join(cmd)}")
                        
                        # 使用subprocess.Popen启动进程
                        import subprocess
                        process = subprocess.Popen(cmd)
                        
                        logger.info(f"{symbol} 交易已提交给执行器进程，PID: {process.pid}")
                    except Exception as e:
                        logger.error(f"使用备用方法执行 {opp.get('symbol', 'Unknown')} 交易时出错: {str(e)}")
                        logger.error(traceback.format_exc())
            
            except Exception as e:
                logger.error(f"执行交易时出错: {str(e)}")
                logger.error(traceback.format_exc())
        
    except Exception as e:
        logger.error(f"保存验证结果或准备执行交易时出错: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

def convert_numpy_types(obj):
    if isinstance(obj, np.bool_):
        return bool(obj)
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    # 处理字典
    elif isinstance(obj, dict):
        return {k: convert_numpy_types(v) for k, v in obj.items()}
    # 处理列表或元组
    elif isinstance(obj, (list, tuple)):
        return [convert_numpy_types(item) for item in obj]
    return obj

def main():
    """主函数"""
    # 添加命令行参数解析
    parser = argparse.ArgumentParser(description="Polygon-以太坊套利二次分析器")
    parser.add_argument("--threads", type=int, default=10, help="线程数量")
    args = parser.parse_args()
    
    # 初始化二次分析器
    logger.info("初始化二次分析器...")
    analyzer = SecondaryArbAnalyzer(num_workers=args.threads)
    
    # 创建事件循环
    loop = asyncio.get_event_loop()
    
    try:
        # 运行分析
        logger.info("开始执行二次分析任务...")
        loop.run_until_complete(analyzer.run_analysis())
        logger.info("二次分析任务执行完成")
    except KeyboardInterrupt:
        logger.info("\n用户中断操作")
    except Exception as e:
        logger.error(f"二次分析执行过程中发生错误: {str(e)}")
        import traceback
        error_trace = traceback.format_exc()
        logger.error(f"错误详情:\n{error_trace}")
    finally:
        loop.close()
        logger.info("=" * 60)
        logger.info("二次分析结束")
        logger.info("=" * 60)

if __name__ == "__main__":
    main() 