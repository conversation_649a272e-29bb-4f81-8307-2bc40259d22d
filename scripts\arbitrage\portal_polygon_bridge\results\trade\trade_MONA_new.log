2025-07-12 11:52:12,630 - trade_MONA - INFO - ================================================================================
2025-07-12 11:52:12,631 - trade_MONA - INFO - 开始执行 MONA 买入交易 - 时间: 2025-07-12 11:52:12
2025-07-12 11:52:12,631 - trade_MONA - INFO - 链: polygon, 投入金额: 108.11 USDT
2025-07-12 11:52:12,632 - trade_MONA - INFO - 代币地址: 0x6968105460f67c3bf751be7c15f92f5286fd0ce5
2025-07-12 11:52:12,642 - trade_MONA - INFO - MONA: 将在polygon链上执行买入，代币地址: 0x6968105460f67c3bf751be7c15f92f5286fd0ce5
2025-07-12 11:52:12,642 - trade_MONA - INFO - MONA: 准备使用KyberSwap在polygon上执行108.11USDT买入MONA交易
2025-07-12 11:53:00,044 - trade_MONA - INFO - MONA: 使用tx_token_change_tracker获取交易 0x5edd5f5524f8c3e6686ac4965a4e2beafe6e78218a2e39b4bd879a42219768a7 的代币数量...
2025-07-12 11:53:13,756 - trade_MONA - INFO - MONA: 从tx_token_change_tracker成功获取到代币数量: 2.327455816283419089
2025-07-12 11:53:13,757 - trade_MONA - INFO - 交易执行成功:
2025-07-12 11:53:13,757 - trade_MONA - INFO -   交易哈希: 0x5edd5f5524f8c3e6686ac4965a4e2beafe6e78218a2e39b4bd879a42219768a7
2025-07-12 11:53:13,757 - trade_MONA - INFO -   实际输出数量: 2.327455816283419 MONA
2025-07-12 11:53:13,757 - trade_MONA - INFO - 开始执行桥接操作，方向: polygon_to_ethereum
2025-07-12 11:53:13,757 - trade_MONA - INFO - ================================================================================
2025-07-12 11:53:13,757 - trade_MONA - INFO - 开始执行 MONA 桥接操作 - 时间: 2025-07-12 11:53:13
2025-07-12 11:53:13,757 - trade_MONA - INFO - 桥接方向: polygon_to_ethereum
2025-07-12 11:53:13,758 - trade_MONA - INFO - 代币数量: 2.327455816283419089 MONA
2025-07-12 11:53:15,053 - trade_MONA - INFO - 从Polygon桥接到以太坊: 2.327455816283419089 MONA
2025-07-12 11:55:20,207 - trade_MONA - ERROR - 桥接操作失败: 桥接过程出错（已重试3次）: 余额不足，需要 2.327455816283419 MONA，但只有 2.75e-16 MONA
2025-07-12 11:55:20,207 - trade_MONA - ERROR - 桥接操作失败: 桥接过程出错（已重试3次）: 余额不足，需要 2.327455816283419 MONA，但只有 2.75e-16 MONA
2025-07-12 11:55:20,208 - trade_MONA - INFO - 交易执行完成，耗时: 187.58秒
2025-07-12 11:55:20,208 - trade_MONA - INFO - ================================================================================
