2025-07-12 09:19:23,020 - trade_BONDLY - INFO - ================================================================================
2025-07-12 09:19:23,020 - trade_BONDLY - INFO - 开始执行 BONDLY 买入交易 - 时间: 2025-07-12 09:19:23
2025-07-12 09:19:23,020 - trade_BONDLY - INFO - 链: ethereum, 投入金额: 209.46 USDT
2025-07-12 09:19:23,020 - trade_BONDLY - INFO - 代币地址: ******************************************
2025-07-12 09:19:23,029 - trade_BONDLY - INFO - BONDLY: 将在ethereum链上执行买入，代币地址: ******************************************
2025-07-12 09:19:23,029 - trade_BONDLY - INFO - BONDLY: 准备使用KyberSwap在ethereum上执行209.46USDT买入BONDLY交易
2025-07-12 09:20:17,078 - trade_BONDLY - INFO - BONDLY: 使用tx_token_change_tracker获取交易 0xea6ef0f1d81943e23d27d5e12a6c2acfada329f9b457d26004c2c439514194f8 的代币数量...
2025-07-12 09:20:19,531 - trade_BONDLY - INFO - BONDLY: 从tx_token_change_tracker成功获取到代币数量: 307211.030977858989347422
2025-07-12 09:20:19,531 - trade_BONDLY - INFO - 交易执行成功:
2025-07-12 09:20:19,531 - trade_BONDLY - INFO -   交易哈希: 0xea6ef0f1d81943e23d27d5e12a6c2acfada329f9b457d26004c2c439514194f8
2025-07-12 09:20:19,531 - trade_BONDLY - INFO -   实际输出数量: 307211.030977859 BONDLY
2025-07-12 09:20:19,531 - trade_BONDLY - INFO - 开始执行桥接操作，方向: ethereum_to_polygon
2025-07-12 09:20:19,532 - trade_BONDLY - INFO - ================================================================================
2025-07-12 09:20:19,532 - trade_BONDLY - INFO - 开始执行 BONDLY 桥接操作 - 时间: 2025-07-12 09:20:19
2025-07-12 09:20:19,532 - trade_BONDLY - INFO - 桥接方向: ethereum_to_polygon
2025-07-12 09:20:19,532 - trade_BONDLY - INFO - 代币数量: 307211.030977858989347422 BONDLY
2025-07-12 09:21:06,139 - trade_BONDLY - ERROR - 执行桥接操作时出错: Bridge初始化失败，已达到最大重试次数: 无法连接到以太坊节点
2025-07-12 09:21:06,141 - trade_BONDLY - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\bridge\pol_bridge\auto_bridge.py", line 56, in __init__
    self.bridge = PolygonEthereumBridge(
                  ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\bridge\pol_bridge\bridge_tokens.py", line 111, in __init__
    raise ConnectionError("无法连接到以太坊节点")
ConnectionError: 无法连接到以太坊节点

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\bridge_arb_executor.py", line 673, in bridge_tokens
    bridge = AutoBridge()
             ^^^^^^^^^^^^
  File "C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\bridge\pol_bridge\auto_bridge.py", line 94, in __init__
    raise ValueError(f"Bridge初始化失败，已达到最大重试次数: {str(last_error)}")
ValueError: Bridge初始化失败，已达到最大重试次数: 无法连接到以太坊节点

2025-07-12 09:21:06,142 - trade_BONDLY - ERROR - 桥接操作失败: 执行桥接操作时出错: Bridge初始化失败，已达到最大重试次数: 无法连接到以太坊节点
2025-07-12 09:21:06,142 - trade_BONDLY - INFO - 交易执行完成，耗时: 103.12秒
2025-07-12 09:21:06,142 - trade_BONDLY - INFO - ================================================================================
2025-07-12 11:57:22,637 - trade_BONDLY - INFO - ================================================================================
2025-07-12 11:57:22,637 - trade_BONDLY - INFO - 开始执行 BONDLY 买入交易 - 时间: 2025-07-12 11:57:22
2025-07-12 11:57:22,638 - trade_BONDLY - INFO - 链: ethereum, 投入金额: 164.18 USDT
2025-07-12 11:57:22,638 - trade_BONDLY - INFO - 代币地址: ******************************************
2025-07-12 11:57:22,648 - trade_BONDLY - INFO - BONDLY: 将在ethereum链上执行买入，代币地址: ******************************************
2025-07-12 11:57:22,648 - trade_BONDLY - INFO - BONDLY: 准备使用KyberSwap在ethereum上执行164.18USDT买入BONDLY交易
2025-07-12 11:58:14,001 - trade_BONDLY - INFO - BONDLY: 使用tx_token_change_tracker获取交易 0x556c6fc90b18f45d790000a90a2f5ddbe24fc39f3c0f4e9b73e29fe593d138e4 的代币数量...
2025-07-12 11:58:18,346 - trade_BONDLY - INFO - BONDLY: 从tx_token_change_tracker成功获取到代币数量: 238143.53016448150119665
2025-07-12 11:58:18,346 - trade_BONDLY - INFO - 交易执行成功:
2025-07-12 11:58:18,346 - trade_BONDLY - INFO -   交易哈希: 0x556c6fc90b18f45d790000a90a2f5ddbe24fc39f3c0f4e9b73e29fe593d138e4
2025-07-12 11:58:18,346 - trade_BONDLY - INFO -   实际输出数量: 238143.5301644815 BONDLY
2025-07-12 11:58:18,346 - trade_BONDLY - INFO - 开始执行桥接操作，方向: ethereum_to_polygon
2025-07-12 11:58:18,346 - trade_BONDLY - INFO - ================================================================================
2025-07-12 11:58:18,346 - trade_BONDLY - INFO - 开始执行 BONDLY 桥接操作 - 时间: 2025-07-12 11:58:18
2025-07-12 11:58:18,347 - trade_BONDLY - INFO - 桥接方向: ethereum_to_polygon
2025-07-12 11:58:18,347 - trade_BONDLY - INFO - 代币数量: 238143.53016448150119665 BONDLY
2025-07-12 11:58:39,689 - trade_BONDLY - INFO - 从以太坊桥接到Polygon: 238143.53016448150119665 BONDLY
2025-07-12 12:16:41,062 - trade_BONDLY - INFO - 桥接操作成功完成
2025-07-12 12:16:41,063 - trade_BONDLY - INFO - 桥接交易哈希: 0x6b7189d32405de502345742a4627e16e57aebab1811798bdace9ec79e878b4dd
2025-07-12 12:16:41,064 - trade_BONDLY - INFO - Polygon交易哈希: None
2025-07-12 12:16:41,065 - trade_BONDLY - INFO - 桥接记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\bridge\BONDLY_20250712_121641_bridge.json
2025-07-12 12:16:41,066 - trade_BONDLY - INFO - 桥接操作成功完成
2025-07-12 12:16:41,066 - trade_BONDLY - INFO - 开始执行卖出操作...
2025-07-12 12:16:41,066 - trade_BONDLY - INFO - ================================================================================
2025-07-12 12:16:41,066 - trade_BONDLY - INFO - 开始执行 BONDLY 卖出交易 - 时间: 2025-07-12 12:16:41
2025-07-12 12:16:41,074 - trade_BONDLY - INFO - 目标链: polygon
2025-07-12 12:16:41,074 - trade_BONDLY - INFO - 目标链代币地址: ******************************************
2025-07-12 12:16:41,074 - trade_BONDLY - INFO - 在polygon链上执行卖出操作
2025-07-12 12:16:41,074 - trade_BONDLY - INFO - 代币地址: ******************************************
2025-07-12 12:16:41,074 - trade_BONDLY - INFO - 卖出数量: 238143.53016448150119665 BONDLY
2025-07-12 12:16:41,074 - trade_BONDLY - INFO - 预期USDT输出: 166.277874 USDT
2025-07-12 12:16:43,047 - trade_BONDLY - INFO - 执行卖出交易，预期USDT输出: 166.277874
2025-07-12 12:17:27,831 - trade_BONDLY - ERROR - swap_result详细信息: {'success': False, 'status': '输出不足', 'message': '', 'error': None, 'chain': 'polygon', 'token_in': '******************************************', 'token_out': '0xc2132d05d31c914a87c6611c10748aeb04b58e8f', 'amount': 238143.5301644815, 'token_in_address': '******************************************', 'token_out_address': '0xc2132d05d31c914a87c6611c10748aeb04b58e8f', 'amount_in_wei': '238143530164481500905472', 'is_native_in': False, 'is_native_out': False}
2025-07-12 12:17:27,832 - trade_BONDLY - ERROR - 解析后的错误信息: error_msg='未知错误', status='输出不足'
2025-07-12 12:17:27,832 - trade_BONDLY - WARNING - 卖出预验证失败: 未知错误
2025-07-12 12:17:27,833 - trade_BONDLY - INFO - 预验证失败，启动最优链选择策略...
2025-07-12 12:17:27,834 - trade_BONDLY - INFO - ================================================================================
2025-07-12 12:17:27,834 - trade_BONDLY - INFO - 开始执行 BONDLY 最优链卖出交易 - 时间: 2025-07-12 12:17:27
2025-07-12 12:17:27,834 - trade_BONDLY - INFO - ================================================================================
2025-07-12 12:17:27,834 - trade_BONDLY - INFO - 开始寻找 BONDLY 的最优卖出链 - 时间: 2025-07-12 12:17:27
2025-07-12 12:17:27,835 - trade_BONDLY - INFO - 代币数量: 238143.53016448150119665 BONDLY
2025-07-12 12:17:30,966 - trade_BONDLY - INFO - Polygon地址: ******************************************
2025-07-12 12:17:30,966 - trade_BONDLY - INFO - Ethereum地址: ******************************************
2025-07-12 12:17:30,966 - trade_BONDLY - INFO - 检查Polygon链卖出价格...
2025-07-12 12:17:34,727 - trade_BONDLY - INFO - 检查Ethereum链卖出价格...
2025-07-12 12:17:45,741 - trade_BONDLY - INFO - Polygon链预期输出: 160.398442 USDT, Gas成本: 0.0036700572534336495 USDT
2025-07-12 12:17:45,741 - trade_BONDLY - INFO - Ethereum链预期输出: 163.122666 USDT, Gas成本: 0.6086563243801815 USDT
2025-07-12 12:17:45,742 - trade_BONDLY - INFO - 两链USDT输出差异: 2.724224000000021 USDT
2025-07-12 12:17:45,742 - trade_BONDLY - INFO - 当前链(polygon)输出: 160.398442 USDT, Gas成本: 0.0036700572534336495 USDT
2025-07-12 12:17:45,743 - trade_BONDLY - INFO - 另一条链(ethereum)输出: 163.122666 USDT, Gas成本: 0.6086563243801815 USDT
2025-07-12 12:17:45,744 - trade_BONDLY - INFO - 输出差异(2.724224000000021 USDT)大于2 USDT，计算跨链成本...
2025-07-12 12:17:45,744 - trade_BONDLY - INFO - 跨链成本: 0.6216585520967276 USDT
2025-07-12 12:17:45,744 - trade_BONDLY - INFO - 当前链净收益: 160.3948 USDT (输出: 160.398442 - Gas: 0.0036700572534336495)
2025-07-12 12:17:45,744 - trade_BONDLY - INFO - 另一条链净收益: 161.8924 USDT (输出: 163.122666 - Gas: 0.6086563243801815 - 跨链: 0.6216585520967276)
2025-07-12 12:17:45,745 - trade_BONDLY - INFO - 另一条链净收益仅高出1.50 USDT <= 1.5 USDT，选择在当前链(polygon)执行卖出
2025-07-12 12:17:45,745 - trade_BONDLY - INFO - 最优卖出链: polygon
2025-07-12 12:17:45,745 - trade_BONDLY - INFO - 选择原因: 另一条链净收益仅高出1.50 USDT <= 1.5 USDT
2025-07-12 12:17:45,745 - trade_BONDLY - INFO - 是否需要跨链: 否
2025-07-12 12:17:45,751 - trade_BONDLY - INFO - 最终执行链: polygon
2025-07-12 12:17:45,752 - trade_BONDLY - INFO - 代币地址: ******************************************
2025-07-12 12:17:45,752 - trade_BONDLY - INFO - 卖出数量: 238143.53016448150119665 BONDLY
2025-07-12 12:17:45,752 - trade_BONDLY - INFO - 在最优链执行卖出交易（无预期输出验证）
2025-07-12 12:18:32,173 - trade_BONDLY - INFO - 使用tx_token_change_tracker获取交易 0xe003cd049b66fb6331344dd36e74557cd78de499883135881bff0d5f1dda8dc6 的USDT数量...
2025-07-12 12:18:35,633 - trade_BONDLY - INFO - 从tx_token_change_tracker成功获取到USDT数量
2025-07-12 12:18:35,633 - trade_BONDLY - INFO - 成功获取到USDT数量: 160.40082
2025-07-12 12:18:35,633 - trade_BONDLY - INFO - 最优链卖出交易执行成功:
2025-07-12 12:18:35,633 - trade_BONDLY - INFO -   执行链: polygon
2025-07-12 12:18:35,633 - trade_BONDLY - INFO -   交易哈希: 0xe003cd049b66fb6331344dd36e74557cd78de499883135881bff0d5f1dda8dc6
2025-07-12 12:18:35,633 - trade_BONDLY - INFO -   实际收到USDT数量: 160.40082
2025-07-12 12:18:35,634 - trade_BONDLY - INFO - 卖出记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\sell\BONDLY_20250712_121835_sell.json
2025-07-12 12:18:35,634 - trade_BONDLY - INFO - 最优链选择策略执行成功
2025-07-12 12:18:35,634 - trade_BONDLY - INFO - 卖出操作成功完成
2025-07-12 12:18:35,634 - trade_BONDLY - INFO - 交易执行完成，耗时: 1273.00秒
2025-07-12 12:18:35,634 - trade_BONDLY - INFO - ================================================================================
2025-07-12 19:07:21,246 - trade_BONDLY - INFO - ================================================================================
2025-07-12 19:07:21,247 - trade_BONDLY - INFO - 开始执行 BONDLY 买入交易 - 时间: 2025-07-12 19:07:21
2025-07-12 19:07:21,247 - trade_BONDLY - INFO - 链: polygon, 投入金额: 141.55 USDT
2025-07-12 19:07:21,247 - trade_BONDLY - INFO - 代币地址: ******************************************
2025-07-12 19:07:21,258 - trade_BONDLY - INFO - BONDLY: 将在polygon链上执行买入，代币地址: ******************************************
2025-07-12 19:07:21,258 - trade_BONDLY - INFO - BONDLY: 准备使用KyberSwap在polygon上执行141.55USDT买入BONDLY交易
2025-07-12 19:08:56,739 - trade_BONDLY - ERROR - 交易执行失败: 在1次尝试后仍然失败: 在1次尝试后仍然失败: 未知错误
2025-07-20 11:34:01,504 - trade_BONDLY - INFO - ================================================================================
2025-07-20 11:34:01,507 - trade_BONDLY - INFO - 开始执行 BONDLY 买入交易 - 时间: 2025-07-20 11:34:01
2025-07-20 11:34:01,508 - trade_BONDLY - INFO - 链: polygon, 投入金额: 277.36 USDT
2025-07-20 11:34:01,508 - trade_BONDLY - INFO - 代币地址: ******************************************
2025-07-20 11:34:01,516 - trade_BONDLY - INFO - BONDLY: 将在polygon链上执行买入，代币地址: ******************************************
2025-07-20 11:34:01,517 - trade_BONDLY - INFO - BONDLY: 准备使用KyberSwap在polygon上执行277.36USDT买入BONDLY交易
2025-07-20 11:34:10,678 - trade_BONDLY - INFO - BONDLY: 使用tx_token_change_tracker获取交易 0x306a212fdb3752b92ac9373bb58ce85f974d0186bfde66c95f1060ffc7f96c31 的代币数量...
2025-07-20 11:34:21,716 - trade_BONDLY - INFO - BONDLY: 从tx_token_change_tracker成功获取到代币数量: 352061.999518450194815184
2025-07-20 11:34:21,716 - trade_BONDLY - INFO - 交易执行成功:
2025-07-20 11:34:21,716 - trade_BONDLY - INFO -   交易哈希: 0x306a212fdb3752b92ac9373bb58ce85f974d0186bfde66c95f1060ffc7f96c31
2025-07-20 11:34:21,716 - trade_BONDLY - INFO -   实际输出数量: 352061.9995184502 BONDLY
2025-07-20 11:34:21,716 - trade_BONDLY - INFO - 开始执行桥接操作，方向: polygon_to_ethereum
2025-07-20 11:34:21,716 - trade_BONDLY - INFO - ================================================================================
2025-07-20 11:34:21,716 - trade_BONDLY - INFO - 开始执行 BONDLY 桥接操作 - 时间: 2025-07-20 11:34:21
2025-07-20 11:34:21,716 - trade_BONDLY - INFO - 桥接方向: polygon_to_ethereum
2025-07-20 11:34:21,716 - trade_BONDLY - INFO - 代币数量: 352061.999518450194815184 BONDLY
2025-07-20 11:34:23,870 - trade_BONDLY - INFO - 从Polygon桥接到以太坊: 352061.999518450194815184 BONDLY
2025-07-20 12:19:38,523 - trade_BONDLY - INFO - 桥接操作成功完成
2025-07-20 12:19:38,524 - trade_BONDLY - INFO - Burn交易哈希: 0xc7c68138b0b41a0ec256c8cfad59511f2663a9e3e63dfbb782691a2f4ab7e22a
2025-07-20 12:19:38,524 - trade_BONDLY - INFO - Claim交易哈希: 0x81c278d913fe4188642d85c91e063f432bf9f8b62dc9f461d7326ff44b866643
2025-07-20 12:19:38,527 - trade_BONDLY - INFO - 桥接记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\bridge\BONDLY_20250720_121938_bridge.json
2025-07-20 12:19:38,527 - trade_BONDLY - INFO - 桥接操作成功完成
2025-07-20 12:19:38,528 - trade_BONDLY - INFO - 开始执行卖出操作...
2025-07-20 12:19:38,528 - trade_BONDLY - INFO - ================================================================================
2025-07-20 12:19:38,529 - trade_BONDLY - INFO - 开始执行 BONDLY 卖出交易 - 时间: 2025-07-20 12:19:38
2025-07-20 12:19:38,537 - trade_BONDLY - INFO - 目标链: ethereum
2025-07-20 12:19:38,537 - trade_BONDLY - INFO - 目标链代币地址: ******************************************
2025-07-20 12:19:38,537 - trade_BONDLY - INFO - 在ethereum链上执行卖出操作
2025-07-20 12:19:38,537 - trade_BONDLY - INFO - 代币地址: ******************************************
2025-07-20 12:19:38,537 - trade_BONDLY - INFO - 卖出数量: 352061.999518450194815184 BONDLY
2025-07-20 12:19:38,538 - trade_BONDLY - INFO - 预期USDT输出: 303.056753 USDT
2025-07-20 12:19:40,129 - trade_BONDLY - INFO - 执行卖出交易，预期USDT输出: 303.056753
2025-07-20 12:20:06,401 - trade_BONDLY - ERROR - swap_result详细信息: {'success': False, 'status': '输出不足', 'message': '', 'error': None, 'chain': 'ethereum', 'token_in': '******************************************', 'token_out': '******************************************', 'amount': 352061.9995184502, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '352061999518450175705088', 'is_native_in': False, 'is_native_out': False}
2025-07-20 12:20:06,402 - trade_BONDLY - ERROR - 解析后的错误信息: error_msg='未知错误', status='输出不足'
2025-07-20 12:20:06,402 - trade_BONDLY - WARNING - 卖出预验证失败: 未知错误
2025-07-20 12:20:06,403 - trade_BONDLY - INFO - 预验证失败，启动最优链选择策略...
2025-07-20 12:20:06,403 - trade_BONDLY - INFO - ================================================================================
2025-07-20 12:20:06,403 - trade_BONDLY - INFO - 开始执行 BONDLY 最优链卖出交易 - 时间: 2025-07-20 12:20:06
2025-07-20 12:20:06,404 - trade_BONDLY - INFO - ================================================================================
2025-07-20 12:20:06,404 - trade_BONDLY - INFO - 开始寻找 BONDLY 的最优卖出链 - 时间: 2025-07-20 12:20:06
2025-07-20 12:20:06,404 - trade_BONDLY - INFO - 代币数量: 352061.999518450194815184 BONDLY
2025-07-20 12:20:08,725 - trade_BONDLY - INFO - Polygon地址: ******************************************
2025-07-20 12:20:08,725 - trade_BONDLY - INFO - Ethereum地址: ******************************************
2025-07-20 12:20:08,726 - trade_BONDLY - INFO - 检查Polygon链卖出价格...
2025-07-20 12:20:11,970 - trade_BONDLY - INFO - 检查Ethereum链卖出价格...
2025-07-20 12:20:17,142 - trade_BONDLY - INFO - Polygon链预期输出: 275.468869 USDT, Gas成本: 0.003904235188662941 USDT
2025-07-20 12:20:17,142 - trade_BONDLY - INFO - Ethereum链预期输出: 278.995198 USDT, Gas成本: 1.3073141337317258 USDT
2025-07-20 12:20:17,142 - trade_BONDLY - INFO - 两链USDT输出差异: 3.5263290000000325 USDT
2025-07-20 12:20:17,142 - trade_BONDLY - INFO - 当前链(ethereum)输出: 278.995198 USDT, Gas成本: 1.3073141337317258 USDT
2025-07-20 12:20:17,142 - trade_BONDLY - INFO - 另一条链(polygon)输出: 275.468869 USDT, Gas成本: 0.003904235188662941 USDT
2025-07-20 12:20:17,142 - trade_BONDLY - INFO - 输出差异(3.5263290000000325 USDT)大于2 USDT，计算跨链成本...
2025-07-20 12:20:17,143 - trade_BONDLY - INFO - 跨链成本: 0.42505710514684064 USDT
2025-07-20 12:20:17,143 - trade_BONDLY - INFO - 当前链净收益: 277.6879 USDT (输出: 278.995198 - Gas: 1.3073141337317258)
2025-07-20 12:20:17,143 - trade_BONDLY - INFO - 另一条链净收益: 275.0399 USDT (输出: 275.468869 - Gas: 0.003904235188662941 - 跨链: 0.42505710514684064)
2025-07-20 12:20:17,143 - trade_BONDLY - INFO - 另一条链净收益仅高出-2.65 USDT <= 1.5 USDT，选择在当前链(ethereum)执行卖出
2025-07-20 12:20:17,143 - trade_BONDLY - INFO - 最优卖出链: ethereum
2025-07-20 12:20:17,143 - trade_BONDLY - INFO - 选择原因: 另一条链净收益仅高出-2.65 USDT <= 1.5 USDT
2025-07-20 12:20:17,143 - trade_BONDLY - INFO - 是否需要跨链: 否
2025-07-20 12:20:17,149 - trade_BONDLY - INFO - 最终执行链: ethereum
2025-07-20 12:20:17,149 - trade_BONDLY - INFO - 代币地址: ******************************************
2025-07-20 12:20:17,149 - trade_BONDLY - INFO - 卖出数量: 352061.999518450194815184 BONDLY
2025-07-20 12:20:17,149 - trade_BONDLY - INFO - 在最优链执行卖出交易（无预期输出验证）
2025-07-20 12:20:37,288 - trade_BONDLY - INFO - 使用tx_token_change_tracker获取交易 0x10e2801c58e3d1e1ac574363a42bb25456d37208f657a25cf97c690ed0ca0af8 的USDT数量...
2025-07-20 12:20:39,770 - trade_BONDLY - INFO - 从tx_token_change_tracker成功获取到USDT数量
2025-07-20 12:20:39,770 - trade_BONDLY - INFO - 成功获取到USDT数量: 278.990009
2025-07-20 12:20:39,770 - trade_BONDLY - INFO - 最优链卖出交易执行成功:
2025-07-20 12:20:39,771 - trade_BONDLY - INFO -   执行链: ethereum
2025-07-20 12:20:39,771 - trade_BONDLY - INFO -   交易哈希: 0x10e2801c58e3d1e1ac574363a42bb25456d37208f657a25cf97c690ed0ca0af8
2025-07-20 12:20:39,771 - trade_BONDLY - INFO -   实际收到USDT数量: 278.990009
2025-07-20 12:20:39,771 - trade_BONDLY - INFO - 卖出记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\sell\BONDLY_20250720_122039_sell.json
2025-07-20 12:20:39,771 - trade_BONDLY - INFO - 最优链选择策略执行成功
2025-07-20 12:20:39,771 - trade_BONDLY - INFO - 卖出操作成功完成
2025-07-20 12:20:39,771 - trade_BONDLY - INFO - 交易执行完成，耗时: 2798.27秒
2025-07-20 12:20:39,772 - trade_BONDLY - INFO - ================================================================================
