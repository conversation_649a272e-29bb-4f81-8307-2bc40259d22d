[{"symbol": "OCEAN", "chain": "ethereum", "buy_chain": "ethereum", "eth_address": "******************************************", "polygon_address": "******************************************", "time": "2025-07-09 11:07:56", "usdt_input": 300.0, "success": true, "amount_bought": "1018.290238471420683112", "tx_hash": "0x85fd9be74cf210bcd4e0930bd6eee217d2566585d27dfc4098af2258635e0ba4", "price": 0.29461148567066403, "error": null, "token_symbol": "OCEAN", "token_name": "OCEAN", "token_decimals": 18, "bridge_success": true, "bridge_tx": "0x35c2065f39a0134e587469c2f3d3ac01336528f4cb0d39acef4d00e8a35feefd", "polygon_tx": null, "sell_success": true, "sell_tx": "0x554c57ed944f8e08896e47c7a679f04ae9f34901f5600ea7eadaa27b08341b32", "usdt_received": "302.470491", "cumulative_profit": 14.609921999999983, "valid_profit_records": 4}, {"symbol": "OCEAN", "chain": "ethereum", "buy_chain": "ethereum", "eth_address": "******************************************", "polygon_address": "******************************************", "time": "2025-07-09 16:13:56", "usdt_input": 300.0, "success": true, "amount_bought": "1016.264014658384914835", "tx_hash": "0x69f53d8014fddfe2de62f48beb257ba21cd104bb2573a83285408e0612246896", "price": 0.29519888107112047, "error": null, "token_symbol": "OCEAN", "token_name": "OCEAN", "token_decimals": 18, "bridge_success": true, "bridge_tx": "0x6ae0d9577aec3b07641e98dcba7573270fa16df105b4f2345754d674aabccea1", "polygon_tx": null, "sell_success": true, "sell_tx": "0xea96653c29a922bebed3e1618a31973e8d01117c01f1959754aeaa8b05f2d2ff", "usdt_received": "304.396343", "cumulative_profit": 14.609921999999983, "valid_profit_records": 4}, {"symbol": "OCEAN", "chain": "ethereum", "buy_chain": "ethereum", "eth_address": "******************************************", "polygon_address": "******************************************", "time": "2025-07-09 17:54:57", "usdt_input": 300.0, "success": true, "amount_bought": "1015.619205481067320103", "tx_hash": "0xa1623be08d87d4cb975ae7bfa0984320f45b93c551d4bff0f0a9899d51178530", "price": 0.2953863006734885, "error": null, "token_symbol": "OCEAN", "token_name": "OCEAN", "token_decimals": 18, "bridge_success": true, "bridge_tx": "0xa0320c38034f3e665678183a4c3ee19cb9773ee7f4519671e3760ccbc9447a6a", "polygon_tx": null, "sell_success": true, "sell_tx": "0xf4d5259c8005d054f742e67bd7760995ebe007b715ea7ea1a71bcd42e94ba6bd", "usdt_received": "303.716307", "cumulative_profit": 14.609921999999983, "valid_profit_records": 4}, {"symbol": "OCEAN", "chain": "ethereum", "buy_chain": "ethereum", "eth_address": "******************************************", "polygon_address": "******************************************", "time": "2025-07-12 14:37:06", "usdt_input": 300.0, "success": true, "amount_bought": "949.346809551724939916", "tx_hash": "0xb300ec908f3d38caf1a1dc24f632809201e7a1d612610b9969f1465f9bdcd338", "price": 0.3160067500955293, "error": null, "token_symbol": "OCEAN", "token_name": "OCEAN", "token_decimals": 18, "bridge_success": true, "bridge_tx": "0x76c151ba4b77e486d924f7f095685388d307777b39eddbc0e5a57606da807e63", "polygon_tx": null, "sell_success": true, "sell_tx": "0xad0d5af5b6b534359d80b42f99ea3fb521618fbd1dd3055ca829967b3d97026d", "usdt_received": "304.026781", "cumulative_profit": 14.609921999999983, "valid_profit_records": 4}, {"symbol": "OCEAN", "chain": "ethereum", "buy_chain": "ethereum", "eth_address": "******************************************", "polygon_address": "******************************************", "time": "2025-07-12 18:04:11", "usdt_input": 283.33, "success": true, "amount_bought": "893.301301109229278979", "tx_hash": "0x83c44cd82dad6f14b41cd6ff804fd80655200862d70e422f9ae75812a0208d9b", "price": 0.3171718205807869, "error": null, "token_symbol": "OCEAN", "token_name": "OCEAN", "token_decimals": 18, "bridge_success": true, "bridge_tx": "0xd9281008ad1e5188dbbf19f6b2a8b170a856771e9f624e84b4637e7c1e5db573", "polygon_tx": null, "sell_error": "在3次尝试后仍然失败: 在5次尝试后仍然失败: 代币余额不足。请求: 893.3013011092293，可用: 5.5043e-14", "cumulative_profit": 14.609921999999983, "valid_profit_records": 4}]